package net.devgrip.server.web.page.project.blob.render.renderers.video;

import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.request.cycle.RequestCycle;

import net.devgrip.server.web.page.project.blob.render.BlobRenderContext;
import net.devgrip.server.web.page.project.blob.render.view.BlobViewPanel;
import net.devgrip.server.web.resource.RawBlobResource;
import net.devgrip.server.web.resource.RawBlobResourceReference;

public class VideoViewPanel extends BlobViewPanel {

	public VideoViewPanel(String id, BlobRenderContext context) {
		super(id, context);
	}

	@Override
	protected void onInitialize() {
		super.onInitialize();
		
		String url = RequestCycle.get().urlFor(new RawBlobResourceReference(), 
				RawBlobResource.paramsOf(context.getProject(), context.getBlobIdent())).toString();

		add(new WebMarkupContainer("video").add(AttributeAppender.append("src", url)));
	}

	@Override
	public void renderHead(IHeaderResponse response) {
		super.renderHead(response);
		response.render(CssHeaderItem.forReference(new VideoViewResourceReference()));
	}

	@Override
	protected boolean isEditSupported() {
		return false;
	}

	@Override
	protected boolean isViewPlainSupported() {
		return false;
	}

}
