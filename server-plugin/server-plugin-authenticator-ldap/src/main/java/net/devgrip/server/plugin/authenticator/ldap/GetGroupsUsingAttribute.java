package net.devgrip.server.plugin.authenticator.ldap;

import net.devgrip.server.annotation.Editable;

import javax.validation.constraints.NotEmpty;

@Editable(order=200,name="GetGroupsUsingAttribute.name")
public class GetGroupsUsingAttribute implements GroupRetrieval {

	private static final long serialVersionUID = 1L;
	
	private String userGroupsAttribute;

	private String groupNameAttribute = "cn";

	@Editable(order=100, name="GetGroupsUsingAttribute.uga.name", description="GetGroupsUsingAttribute.uga.desc")
    @NotEmpty
	public String getUserGroupsAttribute() {
		return userGroupsAttribute;
	}

	public void setUserGroupsAttribute(String userGroupsAttribute) {
		this.userGroupsAttribute = userGroupsAttribute;
	}
	
	@Editable(order=200, name="GetGroupsUsingAttribute.gna.name",description="GetGroupsUsingAttribute.gna.desc")
	@NotEmpty
	public String getGroupNameAttribute() {
		return groupNameAttribute;
	}

	public void setGroupNameAttribute(String groupNameAttribute) {
		this.groupNameAttribute = groupNameAttribute;
	}
		
}
