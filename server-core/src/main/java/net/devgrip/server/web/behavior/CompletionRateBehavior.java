package net.devgrip.server.web.behavior;

import net.devgrip.server.web.page.base.BasePage;
import org.apache.wicket.Component;
import org.apache.wicket.behavior.Behavior;
import org.apache.wicket.markup.ComponentTag;

public abstract class CompletionRateBehavior extends Behavior {
	@Override
	public void onComponentTag(Component component, ComponentTag tag) {
		super.onComponentTag(component, tag);

		long total = getTotal();
		long completed = getCompleted();
		var builder = new StringBuilder("width:20px;height:20px;border-radius:50%;");
		if (total == 0) {
			var page = (BasePage) component.getPage();
			if (page.isDarkMode()) {
				builder.append("border:1px solid var(--gray-9);");
			} else {
				builder.append("border:1px solid var(--gray-9);");
				tag.put("style", builder.toString());
			}
		} else if (completed > total) {
			builder.append("border:1px solid var(--danger-9);background-image:conic-gradient(var(--danger-9) 100%,transparent 0);");
		} else {
			long ratio = completed * 100 / total;
			builder.append(String.format("border:1px solid var(--warning-9);background-image:conic-gradient(var(--warning-9) %s,transparent 0);", ratio + "%"));
		}
		tag.put("style", builder.toString());
	}

	protected abstract long getTotal();
	
	protected abstract long getCompleted();
	
}
