package net.devgrip.server.plugin.imports.gitea;

import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.ChoiceProvider;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.buildspecmodel.inputspec.InputSpec;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.model.support.administration.GlobalIssueSetting;
import net.devgrip.server.model.support.issue.field.spec.FieldSpec;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Editable
public class IssueLabelMapping implements Serializable {

	private static final long serialVersionUID = 1L;

	private String giteaIssueLabel;
	
	private String myIssueField;

	@Editable(order=100, name="GiteaIssueLabelMapping.giteaIssueLabel.name")
	@NotEmpty
	public String getGiteaIssueLabel() {
		return giteaIssueLabel;
	}

	public void setGiteaIssueLabel(String giteaIssueLabel) {
		this.giteaIssueLabel = giteaIssueLabel;
	}

	@Editable(order=200, name="GiteaIssueLabelMapping.myIssueField.name", description="GiteaIssueLabelMapping.myIssueField.desc")
	@ChoiceProvider("getMyIssueFieldChoices")
	@NotEmpty
	public String getMyIssueField() {
		return myIssueField;
	}

	public void setMyIssueField(String myIssueField) {
		this.myIssueField = myIssueField;
	}

	@SuppressWarnings("unused")
	private static List<String> getMyIssueFieldChoices() {
		List<String> choices = new ArrayList<>();
		GlobalIssueSetting issueSetting = AppServer.getInstance(SettingManager.class).getIssueSetting();
		for (FieldSpec field: issueSetting.getFieldSpecs()) {
			if (field.getType().equals(InputSpec.ENUMERATION)) {
				for (String value: field.getPossibleValues()) 
					choices.add(field.getName() + "::" + value);
			}
		}
		return choices;
	}
	
}
