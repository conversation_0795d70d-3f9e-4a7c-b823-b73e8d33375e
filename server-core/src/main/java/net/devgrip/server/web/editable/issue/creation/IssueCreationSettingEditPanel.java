package net.devgrip.server.web.editable.issue.creation;

import java.util.List;

import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.feedback.FencedFeedbackPanel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.request.cycle.RequestCycle;

import net.devgrip.server.model.support.administration.IssueCreationSetting;
import net.devgrip.server.web.ajaxlistener.ConfirmLeaveListener;
import net.devgrip.server.web.editable.BeanContext;
import net.devgrip.server.web.editable.BeanEditor;

abstract class IssueCreationSettingEditPanel extends Panel {

	private final List<IssueCreationSetting> settings;
	
	private final int settingIndex;
	
	public IssueCreationSettingEditPanel(String id, List<IssueCreationSetting> settings, int settingIndex) {
		super(id);
	
		this.settings = settings;
		this.settingIndex = settingIndex;
	}
	
	@Override
	protected void onInitialize() {
		super.onInitialize();
		
		IssueCreationSetting setting;
		if (settingIndex != -1)
			setting = settings.get(settingIndex);
		else
			setting = new IssueCreationSetting();

		Form<?> form = new Form<Void>("form") {

			@Override
			protected void onError() {
				super.onError();
				RequestCycle.get().find(AjaxRequestTarget.class).add(this);
			}
			
		};
		
		form.add(new FencedFeedbackPanel("feedback", form));
		
		form.add(new AjaxLink<Void>("close") {

			@Override
			protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
				super.updateAjaxAttributes(attributes);
				attributes.getAjaxCallListeners().add(new ConfirmLeaveListener(IssueCreationSettingEditPanel.this));
			}

			@Override
			public void onClick(AjaxRequestTarget target) {
				onCancel(target);
			}
			
		});
		
		BeanEditor editor = BeanContext.edit("editor", setting);
		form.add(editor);
		form.add(new AjaxButton("save") {

			@Override
			protected void onSubmit(AjaxRequestTarget target, Form<?> form) {
				super.onSubmit(target, form);

				if (settingIndex != -1) 
					settings.set(settingIndex, setting);
				else 
					settings.add(setting);
				
				onSave(target);
			}
			
		});
		
		form.add(new AjaxLink<Void>("cancel") {

			@Override
			protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
				super.updateAjaxAttributes(attributes);
				attributes.getAjaxCallListeners().add(new ConfirmLeaveListener(IssueCreationSettingEditPanel.this));
			}

			@Override
			public void onClick(AjaxRequestTarget target) {
				onCancel(target);
			}
			
		});
		form.setOutputMarkupId(true);
		
		add(form);
	}

	protected abstract void onSave(AjaxRequestTarget target);
	
	protected abstract void onCancel(AjaxRequestTarget target);

}
