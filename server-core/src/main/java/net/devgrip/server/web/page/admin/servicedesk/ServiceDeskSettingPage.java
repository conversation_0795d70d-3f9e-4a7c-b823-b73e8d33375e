package net.devgrip.server.web.page.admin.servicedesk;

import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.web.editable.BeanContext;
import net.devgrip.server.web.editable.BeanEditor;
import net.devgrip.server.web.page.admin.AdministrationPage;
import net.devgrip.server.web.page.admin.mailservice.MailServicePage;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.model.ResourceModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

public class ServiceDeskSettingPage extends AdministrationPage {

	public ServiceDeskSettingPage(PageParameters params) {
		super(params);
	}

	@Override
	protected void onInitialize() {
		super.onInitialize();
		
		add(new BookmarkablePageLink<Void>("mailService", MailServicePage.class));
		
		ServiceDeskSettingHolder serviceDeskSettingHolder = new ServiceDeskSettingHolder();
		serviceDeskSettingHolder.setServiceDeskSetting(AppServer.getInstance(SettingManager.class).getServiceDeskSetting());
		
		BeanEditor editor = BeanContext.edit("editor", serviceDeskSettingHolder);
		
		Button saveButton = new Button("save") {

			@Override
			public void onSubmit() {
				super.onSubmit();
				
				AppServer.getInstance(SettingManager.class).saveServiceDeskSetting(serviceDeskSettingHolder.getServiceDeskSetting());
				getSession().success(new ResourceModel("ServiceDeskSettingPage.saveSuccess", "Service desk settings have been saved").getObject());
			}
			
		};
		
		Form<?> form = new Form<Void>("form");
		
		form.add(editor);
		form.add(saveButton);
		
		add(form);
	}

	@Override
	protected String newTopbarTitle() {
		return getString("Service_Desk_Settings");
	}
}
