package net.devgrip.server.validation.validator;

import net.devgrip.commons.utils.StringUtils;
import net.devgrip.server.annotation.ProjectQuery;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class ProjectQueryValidator implements ConstraintValidator<ProjectQuery, String> {

	private String message;
	
	@Override
	public void initialize(ProjectQuery constaintAnnotation) {
		message = constaintAnnotation.message();
	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext constraintContext) {
		if (value == null) {
			return true;
		} else {
			try {
				net.devgrip.server.search.entity.project.ProjectQuery.parse(value);
				return true;
			} catch (Exception e) {
				constraintContext.disableDefaultConstraintViolation();
				String message = this.message;
				if (message.length() == 0) {
					if (StringUtils.isNotBlank(e.getMessage())) {
						message = e.getMessage();
					}
					else {
						message = ValidatorFormatter.format("MalformedQuery.message");
					}
				}
				constraintContext.buildConstraintViolationWithTemplate(message).addConstraintViolation();
				return false;
			}
		}
	}
	
}
