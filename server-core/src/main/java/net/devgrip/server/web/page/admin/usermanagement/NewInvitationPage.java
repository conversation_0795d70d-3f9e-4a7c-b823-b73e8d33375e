package net.devgrip.server.web.page.admin.usermanagement;

import net.devgrip.commons.utils.TaskLogger;
import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.entitymanager.UserInvitationManager;
import net.devgrip.server.i18n.I18nManager;
import net.devgrip.server.model.UserInvitation;
import net.devgrip.server.web.component.taskbutton.TaskButton;
import net.devgrip.server.web.component.taskbutton.TaskResult;
import net.devgrip.server.web.component.taskbutton.TaskResult.PlainMessage;
import net.devgrip.server.web.editable.BeanContext;
import net.devgrip.server.web.page.admin.AdministrationPage;
import net.devgrip.server.web.page.admin.mailservice.MailServicePage;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.feedback.FencedFeedbackPanel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.html.panel.Fragment;
import org.apache.wicket.request.mapper.parameter.PageParameters;

public class NewInvitationPage extends AdministrationPage {

	public NewInvitationPage(PageParameters params) {
		super(params);
	}

	@Override
	protected void onInitialize() {
		super.onInitialize();
		
		var mailService = AppServer.getInstance(SettingManager.class).getMailService();
		if (mailService != null) {
			Fragment fragment = new Fragment("content", "inviteFrag", this);
			NewInvitationBean bean = new NewInvitationBean();
			String invitationSentMessage = getString("newInvitation.sent");
			String inviteTitle = getString("newInvitation.title");
			Form<?> form = new Form<Void>("form");
			form.add(BeanContext.edit("editor", bean));
			form.add(new FencedFeedbackPanel("feedback", form));
			form.add(new TaskButton("invite") {

				@Override
				protected String getTitle() {
					return inviteTitle;
				}

				@Override
				protected TaskResult runTask(TaskLogger logger) throws InterruptedException {
					for (String emailAddress: bean.getListOfEmailAddresses()) {
						String defaultMsg = "Sending invitation to '%s'...";
						I18nManager i18nManager = AppServer.getInstance(I18nManager.class);
						String log = String.format(i18nManager.getOrDefault("newInvitation.sendLog", defaultMsg), emailAddress);
						logger.log(log);
						UserInvitation invitation = new UserInvitation();
						invitation.setEmailAddress(emailAddress);
						UserInvitationManager userInvitationManager = AppServer.getInstance(UserInvitationManager.class);
						userInvitationManager.create(invitation);
						userInvitationManager.sendInvitationEmail(invitation);
						if (Thread.interrupted())
							throw new InterruptedException();
					}
					return new TaskResult(true, new PlainMessage(invitationSentMessage));
				}

				@Override
				protected void onCompleted(AjaxRequestTarget target, boolean successful) {
					if (successful)
						setResponsePage(InvitationListPage.class);
					}
					
			});
			fragment.add(form);
			add(fragment);
		} else {
			Fragment fragment = new Fragment("content", "noMailServiceFrag", this);
			fragment.add(new BookmarkablePageLink<Void>("mailSetting", MailServicePage.class));
			add(fragment);
		}
	}

	@Override
	protected String newTopbarTitle() {
		return getString("newInvitation.title");
	}

}
