package net.devgrip.server.buildspec.job.trigger;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.buildspec.job.Job;
import net.devgrip.server.buildspec.job.JobDependency;
import net.devgrip.server.buildspec.job.TriggerMatch;
import net.devgrip.server.buildspec.param.instance.ParamInstances;
import net.devgrip.server.event.project.ProjectEvent;
import net.devgrip.server.event.project.build.BuildFinished;
import net.devgrip.server.model.Build;
import net.devgrip.server.model.Build.Status;

import static net.devgrip.server.buildspec.param.ParamUtils.isCoveredBy;
import static net.devgrip.server.buildspec.param.ParamUtils.resolveParams;
import static java.util.stream.Collectors.toSet;

@Editable(order=500, name="DependencyFinishedTrigger.name")
public class DependencyFinishedTrigger extends JobTrigger {

	private static final long serialVersionUID = 1L;

	@Override
	protected TriggerMatch triggerMatches(ProjectEvent event, Job job) {
		if (event instanceof BuildFinished) {
			BuildFinished buildFinished = (BuildFinished) event;
			Build build = buildFinished.getBuild();
			for (JobDependency dependency: job.getJobDependencies()) {
				if (dependency.getJobName().equals(build.getJobName()) 
						&& (!dependency.isRequireSuccessful() || build.getStatus() == Status.SUCCESSFUL)) {
					var secretParamNames = dependency.getParamMatrix().stream()
							.filter(ParamInstances::isSecret)
							.map(ParamInstances::getName)
							.collect(toSet());					
					for (var paramMap: resolveParams(null, null, 
							dependency.getParamMatrix(), dependency.getExcludeParamMaps())) {
						if (isCoveredBy(build.getParamMap(), paramMap, secretParamNames)) {
							String defaultMsg = "Dependency job '%s' is finished";
							String reason = String.format(getI18nManager().getOrDefault("DependencyFinishedTrigger.reason", defaultMsg), dependency.getJobName());
							return new TriggerMatch(build.getRefName(), build.getRequest(), build.getIssue(), getParamMatrix(),
									getExcludeParamMaps(), reason);
						}
					}
				}
			}
		}
		return null;
	}

	@Override
	public String getTriggerDescription() {
		return getI18nManager().getOrDefault("DependencyFinishedTrigger.desc", "When dependency jobs finished");
	}

}
