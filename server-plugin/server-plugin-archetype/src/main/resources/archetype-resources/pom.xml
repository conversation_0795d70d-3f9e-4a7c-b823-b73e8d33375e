<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
#if (\${groupId} == "net.devgrip")
	<artifactId>\${artifactId}</artifactId>
	<parent>
		<groupId>net.devgrip</groupId>
		<artifactId>server-plugin</artifactId>
		<version>${project.version}</version>
	</parent>
	<properties>
		<moduleClass>\${package}.PluginModule</moduleClass>
	</properties>
#else
	<groupId>\${groupId}</groupId>
	<artifactId>\${artifactId}</artifactId>
	<version>\${version}</version>
	<build>
        <finalName>\${project.groupId}.\${project.artifactId}-\${project.version}</finalName>  
        <pluginManagement> 
            <plugins> 
				<plugin>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.6.0</version>
					<configuration>
						<source>1.8</source>
						<target>1.8</target>
					</configuration>
				</plugin>
            </plugins> 
        </pluginManagement>  
        <plugins>
			<plugin>
				<groupId>net.devgrip</groupId>
				<artifactId>plugin-maven</artifactId>
				<version>2.6.9</version>
				<executions>
					<execution>
						<?m2e execute onConfiguration,onIncremental?>
						<id>generate-resources</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>generate-resources</goal>
						</goals>
					</execution>
					<execution>
						<id>package-artifacts</id>
						<phase>package</phase>
						<goals>
							<goal>package-artifacts</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.0.2</version>
				<executions>
					<execution>
						<id>default-jar</id>
						<phase>none</phase>
						<configuration>
							<finalName>unwanted</finalName>
							<classifier>unwanted</classifier>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.0.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
        </plugins>
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
			<resource>
				<directory>target/generated-resources</directory>
			</resource>
		</resources>
	</build>	
	<dependencies>
		<dependency>
			<groupId>net.devgrip</groupId>
			<artifactId>server-product</artifactId>
			<version>\${devgrip.version}</version>
		</dependency>
	</dependencies>
	<repositories>
		<repository>
			<id>devgrip</id>
			<url>https://app.devgrip.net/devgrip/~maven</url>
			<releases>
				<enabled>true</enabled>
				<updatePolicy>never</updatePolicy>
				<checksumPolicy>fail</checksumPolicy>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
				<checksumPolicy>fail</checksumPolicy>
			</snapshots>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>devgrip</id>
			<url>https://app.devgrip.net/devgrip/~maven</url>
			<releases>
				<enabled>true</enabled>
				<updatePolicy>never</updatePolicy>
				<checksumPolicy>fail</checksumPolicy>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>never</updatePolicy>
				<checksumPolicy>fail</checksumPolicy>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<devgrip.version>${project.version}</devgrip.version>
		<moduleClass>\${package}.PluginModule</moduleClass>
	</properties>
#end
</project>
