package net.devgrip.server.plugin.notification.discord;

import java.util.List;

import com.beust.jcommander.internal.Lists;

import net.devgrip.commons.loader.AbstractPluginModule;
import net.devgrip.server.web.page.project.setting.ContributedProjectSetting;
import net.devgrip.server.web.page.project.setting.ProjectSettingContribution;

/**
 * NOTE: Do not forget to rename moduleClass property defined in the pom if you've renamed this class.
 *
 */
public class DiscordModule extends AbstractPluginModule {

	@Override
	protected void configure() {
		super.configure();
		
		// put your guice bindings here
		contribute(ProjectSettingContribution.class, new ProjectSettingContribution() {
			
			@SuppressWarnings("unchecked")
			@Override
			public List<Class<? extends ContributedProjectSetting>> getSettingClasses() {
				return Lists.newArrayList(DiscordNotificationSetting.class);
			}
			
		});
		bind(DiscordNotificationManager.class);
	}

}
