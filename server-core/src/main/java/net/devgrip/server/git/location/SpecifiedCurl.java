package net.devgrip.server.git.location;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;

import javax.validation.constraints.NotEmpty;

@Editable(name="Use_Specified_Curl", order=200)
public class SpecifiedCurl extends CurlLocation {

	private static final long serialVersionUID = 1L;
	
	private String curlPath;
	
	@Editable(name="Specified_Curl_Path_Name", description="Specified_Curl_Path_Desc")
	@OmitName
	@NotEmpty
	public String getCurlPath() {
		return curlPath;
	}

	public void setCurlPath(String curlPath) {
		this.curlPath = curlPath;
	}

	@Override
	public String getExecutable() {
		return curlPath;
	}

}
