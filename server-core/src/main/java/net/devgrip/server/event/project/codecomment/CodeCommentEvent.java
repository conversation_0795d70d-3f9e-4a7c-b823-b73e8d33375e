package net.devgrip.server.event.project.codecomment;

import java.util.Date;

import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.CodeCommentManager;
import net.devgrip.server.web.UrlManager;
import net.devgrip.server.event.project.ProjectEvent;
import net.devgrip.server.model.CodeComment;
import net.devgrip.server.model.User;

public abstract class CodeCommentEvent extends ProjectEvent {

	private static final long serialVersionUID = 1L;
	
	private final Long commentId;
	
	/**
	 * @param comment
	 * @param user
	 * @param date
	 */
	public CodeCommentEvent(User user, Date date, CodeComment comment) {
		super(user, date, comment.getProject());
		commentId = comment.getId();
	}

	public CodeComment getComment() {
		return AppServer.getInstance(CodeCommentManager.class).load(commentId);
	}

	@Override
	public String getUrl() {
		return AppServer.getInstance(UrlManager.class).urlFor(getComment());
	}
	
}
