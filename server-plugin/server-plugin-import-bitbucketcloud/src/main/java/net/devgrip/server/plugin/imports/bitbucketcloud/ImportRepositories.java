package net.devgrip.server.plugin.imports.bitbucketcloud;

import net.devgrip.server.annotation.ChoiceProvider;
import net.devgrip.server.annotation.ClassValidating;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.ShowCondition;
import net.devgrip.server.security.SecurityUtils;
import net.devgrip.server.security.permission.CreateChildren;
import net.devgrip.server.util.ComponentContext;
import net.devgrip.server.util.EditContext;
import net.devgrip.server.validation.Validatable;
import net.devgrip.server.validation.validator.ValidatorFormatter;
import net.devgrip.server.web.editable.BeanEditor;

import javax.validation.ConstraintValidatorContext;
import javax.validation.constraints.Size;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Editable
@ClassValidating
public class ImportRepositories extends ImportWorkspace implements Validatable {

	private static final long serialVersionUID = 1L;

	private String parentOneDevProject;

	private boolean all;

	private boolean includeForks;

	private List<String> bitbucketRepositories;

	@Editable(order=200, name="BitbucketCloud.ImportRepositories.parent.name", description = "BitbucketCloud.ImportRepositories.parent.desc")
	@ChoiceProvider("getParentOneDevProjectChoices")
	public String getParentOneDevProject() {
		return parentOneDevProject;
	}

	public void setParentOneDevProject(String parentOneDevProject) {
		this.parentOneDevProject = parentOneDevProject;
	}

	@SuppressWarnings("unused")
	private static List<String> getParentOneDevProjectChoices() {
		return SecurityUtils.getAuthorizedProjects(new CreateChildren()).stream()
				.map(it->it.getPath()).sorted().collect(Collectors.toList());
	}

	@Editable(order=300, name="BitbucketCloud.ImportRepositories.all.name")
	public boolean isAll() {
		return all;
	}

	public void setAll(boolean all) {
		this.all = all;
	}

	private static boolean isAllEnabled() {
		return (Boolean)EditContext.get().getInputValue("all");
	}

	@SuppressWarnings("unused")
	private static boolean isAllDisabled() {
		return !isAllEnabled();
	}

	@Editable(order=400, name = "BitbucketCloud.ImportRepositories.includeForks.name", description="BitbucketCloud.ImportRepositories.includeForks.desc")
	@ShowCondition("isAllEnabled")
	public boolean isIncludeForks() {
		return includeForks;
	}

	public void setIncludeForks(boolean includeForks) {
		this.includeForks = includeForks;
	}

	@Editable(order=500, name="BitbucketCloud.ImportRepositories.bitbucktRepoToImport.name")
	@ChoiceProvider("getBitbucketRepositoryChoices")
	@ShowCondition("isAllDisabled")
	@Size(min=1, message="{AtLeastOneRepoShoudBeSelected}")
	public List<String> getBitbucketRepositories() {
		return bitbucketRepositories;
	}

	public void setBitbucketRepositories(List<String> bitbucketRepositories) {
		this.bitbucketRepositories = bitbucketRepositories;
	}

	@SuppressWarnings("unused")
	private static List<String> getBitbucketRepositoryChoices() {
		BeanEditor editor = ComponentContext.get().getComponent().findParent(BeanEditor.class);
		ImportRepositories repositories = (ImportRepositories) editor.getModelObject();
		String workspace = (String) EditContext.get().getInputValue("workspace");
		return repositories.server.listRepositories(workspace, true);
	}

	public Collection<String> getImportRepositories() {
		if (isAll())
			return server.listRepositories(getWorkspace(), isIncludeForks());
		else
			return getBitbucketRepositories();
	}

	@Override
	public boolean isValid(ConstraintValidatorContext context) {
		if (parentOneDevProject == null && !SecurityUtils.canCreateRootProjects()) {
			context.disableDefaultConstraintViolation();
			var errorMessage = ValidatorFormatter.format("NoPermissionToImportAsRootProjects");
			context.buildConstraintViolationWithTemplate(errorMessage)
					.addPropertyNode("parentOneDevProject")
					.addConstraintViolation();
			return false;
		} else {
			return true;
		}
	}

}
