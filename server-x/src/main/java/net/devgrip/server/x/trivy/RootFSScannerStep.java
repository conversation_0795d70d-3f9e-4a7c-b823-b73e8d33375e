package net.devgrip.server.x.trivy;

import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Interpolative;
import net.devgrip.server.annotation.NoSpace;
import net.devgrip.server.annotation.SubPath;
import net.devgrip.server.buildspec.step.StepGroup;
import net.devgrip.server.i18n.I18nManager;

@Editable(
   order = 160,
   name = "RootFSScannerStep.name",
   group = StepGroup.SECURITY_COMPLIANCE_I18N_KEY,
   descriptionProvider = "getStepDescription"
)
public class RootFSScannerStep extends TrivyScanStep {
   private static final long serialVersionUID = 1L;
   private String scanPath;

   @Editable(
      order = 100,
      name = "RootFSScannerStep.scanPath.name",
      placeholder = "RootFSScannerStep.scanPath.placeholder",
      description = "RootFSScannerStep.scanPath.desc"
   )
   @Interpolative(
      variableSuggester = "suggestVariables"
   )
   @SubPath
   @NoSpace
   @Override
   public String getScanPath() {
      return this.scanPath;
   }

   public void setScanPath(String scanPath) {
      this.scanPath = scanPath;
   }

   @Override
   protected String getScanCommand() {
      StringBuilder commandBuilder = new StringBuilder(
         "outputs_dir=\"/devgrip-build/workspace/.trivy-outputs\"\nmkdir -p $outputs_dir\nrm -f $outputs_dir/*\ntrivy rootfs "
      );
      commandBuilder.append(this.getScanOptions()).append(" -o /devgrip-build/workspace/.trivy-outputs/report.json ");
      if (this.getScanPath() != null) {
         commandBuilder.append(this.getScanPath());
      } else {
         commandBuilder.append(".");
      }

      return commandBuilder.toString();
   }

   private static String getStepDescription() {
      String description = getI18nManager().getOrDefault("RootFSScannerStep.desc","This step runs trivy rootfs scanner to scan various <a href='https://aquasecurity.github.io/trivy/v0.50/docs/coverage/language/#supported-languages' target='_blank'>distribution files</a>. It can only be executed by a docker aware executor and is recommended to run against staging area of your project");
      if (!isSubscriptionActive()) {
         description = description
            + getI18nManager().get("DescWithNoSubscription");
      }

      return description;
   }

   private static I18nManager getI18nManager() {
      return AppServer.getInstance(I18nManager.class);
   }
}
