div.side-info {
	border-radius: 0 0 0.42rem 0;
	border-left: 1px solid var(--light-gray);
	margin: -2rem -2.25rem -2rem 2rem; 
	flex-shrink: 0;
	transition: all var(--transition-duration) ease;
	width: 360px;
	background: var(--info-3);
}
.dark-mode div.side-info {
	border-left: 1px solid var(--dark-mode-light-dark);
	background: var(--dark-info-3);
}
div.side-info>.header, div.side-info>.footer {
	padding: 1.5rem 2rem;
}
div.side-info>.body {
	padding: 2rem 2.25rem;
}
div.side-info>.header .close {
	border-left: 1px solid var(--secondary);
	margin-left: 0.6rem;
	padding-left: 0.6rem;
	display: none;
}
.dark-mode div.side-info>.header .close {
	border-left-color: var(--dark-mode-dark-gray);
}

a.side-info {
	display: none;
	align-items: center;
}
@media(max-width: 990px) {
	div.side-info {
	    box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
		border-left: none;
		position: fixed;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 1050;
		margin: 0;
	}
	.dark-mode div.side-info {
		box-shadow: 0 0 30px rgb(0 0 0 / 50%);	
	}
	div.side-info.closed {
		right: -360px;
		box-shadow: none;
	}
	div.side-info>.header .close {
		display: flex;
	}
	div.side-info>.body {
		overflow: auto;
	}
	a.side-info {
		display: inline-flex;
	}
}

.hide-side-info div.side-info {
    box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
	border-left: none;
	position: fixed;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 1050;
	margin: 0;
}
.dark-mode .hide-side-info div.side-info {
	box-shadow: 0 0 30px rgb(0 0 0 / 50%);	
}

.hide-side-info div.side-info.closed {
	right: -360px;
	box-shadow: none;
}
.hide-side-info div.side-info>.header .close {
	display: flex;
}
.hide-side-info div.side-info>.body {
	overflow: auto;
}
.hide-side-info a.side-info {
	display: inline-flex;
}
