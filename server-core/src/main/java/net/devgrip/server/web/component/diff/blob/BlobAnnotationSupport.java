package net.devgrip.server.web.component.diff.blob;

import net.devgrip.commons.utils.PlanarRange;
import net.devgrip.server.codequality.CodeProblem;
import net.devgrip.server.codequality.CoverageStatus;
import net.devgrip.server.model.CodeComment;
import net.devgrip.server.util.Pair;
import net.devgrip.server.web.util.DiffPlanarRange;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.Collection;
import java.util.Map;

public interface BlobAnnotationSupport extends Serializable {

	@Nullable
	DiffPlanarRange getMarkRange();

	String getMarkUrl(DiffPlanarRange markRange);

	Map<CodeComment, PlanarRange> getOldComments();

	Map<CodeComment, PlanarRange> getNewComments();

	Collection<CodeProblem> getOldProblems();

	Collection<CodeProblem> getNewProblems();

	Map<Integer, CoverageStatus> getOldCoverages();

	Map<Integer, CoverageStatus> getNewCoverages();

	DiffPlanarRange getCommentRange(CodeComment comment);

	@Nullable
	Pair<CodeComment, DiffPlanarRange> getOpenComment();

	void onOpenComment(AjaxRequestTarget target, CodeComment comment, DiffPlanarRange commentRange);

	void onAddComment(AjaxRequestTarget target, DiffPlanarRange commentRange);

	Component getCommentContainer();

}
