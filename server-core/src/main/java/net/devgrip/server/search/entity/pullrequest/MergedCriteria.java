package net.devgrip.server.search.entity.pullrequest;

import javax.annotation.Nullable;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.From;
import javax.persistence.criteria.Predicate;

import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequest.Status;
import net.devgrip.server.util.ProjectScope;

public class MergedCriteria extends StatusCriteria {

	private static final long serialVersionUID = 1L;

	@Override
	public Predicate getPredicate(@Nullable ProjectScope projectScope, CriteriaQuery<?> query, From<PullRequest, PullRequest> from, CriteriaBuilder builder) {
		return builder.equal(from.get(PullRequest.PROP_STATUS), Status.MERGED);
	}

	@Override
	public boolean matches(PullRequest request) {
		return request.isMerged();
	}

	@Override
	public String toStringWithoutParens() {
		return PullRequestQuery.getRuleName(PullRequestQueryLexer.Merged);
	}

	@Override
	public Status getStatus() {
		return Status.MERGED;
	}

}
