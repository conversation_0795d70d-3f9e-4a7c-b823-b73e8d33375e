package net.devgrip.server.x.stats.issue;

import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.IssueStateHistoryManager;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.model.support.issue.StateSpec;
import net.devgrip.server.util.ProjectScope;
import net.devgrip.server.web.component.chart.line.Line;
import net.devgrip.server.web.component.chart.line.LineChartPanel;
import net.devgrip.server.web.component.chart.line.LineSeries;
import net.devgrip.server.web.util.StatsGroup;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.wicket.Component;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public abstract class IssueStateTrendStatsPanel extends IssueStatsPanel {
   public IssueStateTrendStatsPanel(String id) {
      super(id);
   }

   @Override
   protected void onInitialize() {
      super.onInitialize();
      this.add(
         new Component[]{
            new LineChartPanel(
               "chart",
               new LoadableDetachableModel<LineSeries>() {
                  protected LineSeries load() {
                     StatsGroup statsGroup = IssueStateTrendStatsPanel.this.getStatsGroup();
                     Map<Integer, Map<String, Integer>> statsData = ((IssueStateHistoryManager) AppServer.getInstance(IssueStateHistoryManager.class))
                        .queryTrendStats(
                           new ProjectScope(IssueStateTrendStatsPanel.this.getProject(), true, true),
                           IssueStateTrendStatsPanel.this.getIssueFilter().getCriteria(),
                           IssueStateTrendStatsPanel.this.getStartDate(),
                           IssueStateTrendStatsPanel.this.getEndDate(),
                           statsGroup
                        );
                     List<Pair<String, Map<String, Integer>>> normalizedStatsData = statsGroup.normalizeData(statsData, null);
                     List<String> xAxisValues = normalizedStatsData.stream().<String>map(Pair::getLeft).collect(Collectors.toList());
                     List<Line> lines = new ArrayList<>();

                     for (StateSpec state : ((SettingManager) AppServer.getInstance(SettingManager.class)).getIssueSetting().getStateSpecs()) {
                        ArrayList<Integer> yAxisValues = new ArrayList<>();
                        boolean found = false;

                        for (Pair<String, Map<String, Integer>> dataPoint : normalizedStatsData) {
                           Integer yAxisValue = (dataPoint.getRight()).get(state.getName());
                           if (yAxisValue != null) {
                              found = true;
                              yAxisValues.add(yAxisValue);
                           } else {
                              yAxisValues.add(0);
                           }
                        }

                        if (found) {
                           lines.add(new Line(state.getName(), yAxisValues, state.getColor(), "frequency", null));
                        }
                     }

                     return new LineSeries(null, xAxisValues, lines, null, 0, null);
                  }
               }
            )
         }
      );
   }
}
