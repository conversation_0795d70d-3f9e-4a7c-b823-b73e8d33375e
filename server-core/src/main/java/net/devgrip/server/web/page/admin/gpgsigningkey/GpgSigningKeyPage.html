<wicket:extend>
	<div class="card gpg-signing-key">
		<div class="card-body">
			<div class="alert alert-light-info alert-notice mb-4">
				<wicket:message key="GpgServerKeyPage.desc"></wicket:message>
			</div>
			<div wicket:id="content"></div>
		</div>
	</div>
	<wicket:fragment wicket:id="undefinedFrag">
		<a wicket:id="generate" class="btn btn-primary"><wicket:message key="GpgServerKeyPage.gen"></wicket:message></a>
	</wicket:fragment>
	<wicket:fragment wicket:id="definedFrag">
		<dl>
			<dt><wicket:message key="GpgServerKeyPage.email"></wicket:message></dt>
			<dd wicket:id="emailAddress"></dd>
			<dt><wicket:message key="GpgServerKeyPage.keyId"></wicket:message></dt>
			<dd wicket:id="keyID"></dd>
			<dt class="mb-2"><wicket:message key="GpgServerKeyPage.pk"></wicket:message><a wicket:id="copyPublicKey" title="Copy public key" wicket:message="title:GpgServerKeyPage.copy"><wicket:svg href="copy" class="icon"/></a></dt>
			<dd>
				<pre wicket:id="publicKey" class="font-size-sm bg-light p-3 rounded"></pre>
			</dd>
		</dl>
		<a wicket:id="delete" class="btn btn-light-danger"><wicket:message key="Delete"></wicket:message></a>
	</wicket:fragment>
</wicket:extend>
