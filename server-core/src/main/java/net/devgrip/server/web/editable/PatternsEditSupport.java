package net.devgrip.server.web.editable;

import com.google.common.collect.Lists;
import net.devgrip.commons.codeassist.InputSuggestion;
import net.devgrip.commons.codeassist.parser.TerminalExpect;
import net.devgrip.server.annotation.Patterns;
import net.devgrip.server.util.Hints;
import net.devgrip.server.util.ReflectionUtils;
import net.devgrip.server.web.behavior.PatternSetAssistBehavior;
import net.devgrip.server.web.editable.string.StringPropertyEditor;
import net.devgrip.server.web.editable.string.StringPropertyViewer;
import org.apache.wicket.model.IModel;

import java.lang.reflect.Method;
import java.util.List;

@SuppressWarnings("serial")
public class PatternsEditSupport implements EditSupport {
	@Override
	public PropertyContext<?> getEditContext(PropertyDescriptor descriptor) {
		Method propertyGetter = descriptor.getPropertyGetter();
		Patterns patterns = propertyGetter.getAnnotation(Patterns.class);
        if (patterns != null) {
        	if (propertyGetter.getReturnType() == String.class) {
        		return new PropertyContext<String>(descriptor) {

    				@Override
    				public PropertyViewer renderForView(String componentId, IModel<String> model) {
    					return new StringPropertyViewer(componentId, descriptor, model.getObject());
    				}

    				@Override
    				public PropertyEditor<String> renderForEdit(String componentId, IModel<String> model) {
    		        	return new StringPropertyEditor(componentId, descriptor, model).setInputAssist(
    		        			new PatternSetAssistBehavior() {

							@SuppressWarnings("unchecked")
							@Override
							protected List<InputSuggestion> suggest(String matchWith) {
								String suggestionMethod = patterns.suggester();
								if (suggestionMethod.length() != 0) {
									return (List<InputSuggestion>) ReflectionUtils.invokeStaticMethod(
											descriptor.getBeanClass(), suggestionMethod, new Object[] {matchWith});
								} else {
									return Lists.newArrayList();
								}
							}
							
							@Override
							protected List<String> getHints(TerminalExpect terminalExpect) {
								return Lists.newArrayList(
									Hints.HINT_Need_Quoted,
										patterns.path()? Hints.HINT_Path_Wildcard_2: Hints.HINT_Path_Wildcard_1
								);
							}
							
						});
    				}
        			
        		};
        	} else {
	    		throw new RuntimeException("Annotation 'Patterns' should be applied to property "
	    				+ "of type 'String'");
        	}
        } else {
            return null;
        }
	}

	@Override
	public int getPriority() {
		return DEFAULT_PRIORITY;
	}
	
}
