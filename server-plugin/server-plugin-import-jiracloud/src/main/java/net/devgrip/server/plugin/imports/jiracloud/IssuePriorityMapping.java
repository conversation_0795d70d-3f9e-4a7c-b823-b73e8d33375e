package net.devgrip.server.plugin.imports.jiracloud;

import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.ChoiceProvider;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.buildspecmodel.inputspec.InputSpec;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.model.support.administration.GlobalIssueSetting;
import net.devgrip.server.model.support.issue.field.spec.FieldSpec;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Editable
public class IssuePriorityMapping implements Serializable {

	private static final long serialVersionUID = 1L;

	private String jiraIssuePriority;
	
	private String oneDevIssueField;

	@Editable(order=100, name="IssuePriorityMapping.issuePriority.name")
	@NotEmpty
	public String getJiraIssuePriority() {
		return jiraIssuePriority;
	}

	public void setJiraIssuePriority(String jiraIssuePriority) {
		this.jiraIssuePriority = jiraIssuePriority;
	}

	@Editable(order=200, name="IssuePriorityMapping.devgripIssueField.name", description="IssuePriorityMapping.devgripIssueField.desc")
	@ChoiceProvider("getOneDevIssueFieldChoices")
	@NotEmpty
	public String getOneDevIssueField() {
		return oneDevIssueField;
	}

	public void setOneDevIssueField(String oneDevIssueField) {
		this.oneDevIssueField = oneDevIssueField;
	}

	@SuppressWarnings("unused")
	private static List<String> getOneDevIssueFieldChoices() {
		List<String> choices = new ArrayList<>();
		GlobalIssueSetting issueSetting = AppServer.getInstance(SettingManager.class).getIssueSetting();
		for (FieldSpec field: issueSetting.getFieldSpecs()) {
			if (field.getType().equals(InputSpec.ENUMERATION)) {
				for (String value: field.getPossibleValues()) 
					choices.add(field.getName() + "::" + value);
			}
		}
		return choices;
	}
	
}
