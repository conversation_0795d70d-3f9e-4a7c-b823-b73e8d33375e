package net.devgrip.server.plugin.imports.gitea;

import net.devgrip.server.annotation.ChoiceProvider;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.util.ComponentContext;
import net.devgrip.server.web.editable.BeanEditor;

import java.io.Serializable;
import java.util.List;

@Editable
public class ImportOrganization implements Serializable {

	private static final long serialVersionUID = 1L;
	
	ImportServer server;
	
	private String organization;
	
	private boolean includeForks;

	@Editable(order=100, name="GiteaImportOrganization.org.name", description= "GiteaImportOrganization.org.desc")
	@ChoiceProvider("getOrganizationChoices")
	public String getOrganization() {
		return organization;
	}

	public void setOrganization(String organization) {
		this.organization = organization;
	}
	
	@SuppressWarnings("unused")
	private static List<String> getOrganizationChoices() {
		BeanEditor editor = ComponentContext.get().getComponent().findParent(BeanEditor.class);
		ImportOrganization setting = (ImportOrganization) editor.getModelObject();
		return setting.server.listOrganizations();
	}
	
	@Editable(order=200, name = "GiteaImportOrganization.includeForks.name", description="GiteaImportOrganization.includeForks.desc")
	public boolean isIncludeForks() {
		return includeForks;
	}

	public void setIncludeForks(boolean includeForks) {
		this.includeForks = includeForks;
	}
	
}
