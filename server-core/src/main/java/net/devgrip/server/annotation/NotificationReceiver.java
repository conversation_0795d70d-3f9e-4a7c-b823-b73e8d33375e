package net.devgrip.server.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

import net.devgrip.server.validation.validator.NotificationReceiverValidator;

@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy=NotificationReceiverValidator.class) 
public @interface NotificationReceiver {

	String message() default ""; 
	
	Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
    
}
