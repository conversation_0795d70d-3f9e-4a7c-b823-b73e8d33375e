package net.devgrip.server.validation.validator;

import net.devgrip.server.annotation.ReviewRequirement;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class ReviewRequirementValidator implements ConstraintValidator<ReviewRequirement, String> {
	
	private String message;
	
	@Override
	public void initialize(ReviewRequirement constaintAnnotation) {
		message = constaintAnnotation.message();
	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext constraintContext) {
		if (value == null) {
			return true;
		} else {
			try {
				net.devgrip.server.util.reviewrequirement.ReviewRequirement.parse(value);
				return true;
			} catch (Exception e) {
				constraintContext.disableDefaultConstraintViolation();
				String message = this.message;
				if (message.length() == 0) {
					if (StringUtils.isNotBlank(e.getMessage()))
						message = e.getMessage();
					else
						message = ValidatorFormatter.format("MalformedReviewRequirement");
				}
				constraintContext.buildConstraintViolationWithTemplate(message).addConstraintViolation();
				return false;
			}
		}
	}
}
