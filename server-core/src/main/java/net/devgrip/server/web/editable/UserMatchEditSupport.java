package net.devgrip.server.web.editable;

import net.devgrip.server.util.usermatch.Anyone;
import net.devgrip.server.web.behavior.UserMatchBehavior;
import net.devgrip.server.annotation.UserMatch;
import net.devgrip.server.web.editable.string.StringPropertyEditor;
import net.devgrip.server.web.editable.string.StringPropertyViewer;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.ResourceModel;

import java.lang.reflect.Method;

public class UserMatchEditSupport implements EditSupport {

	@Override
	public PropertyContext<?> getEditContext(PropertyDescriptor descriptor) {
		Method propertyGetter = descriptor.getPropertyGetter();
        if (propertyGetter.getAnnotation(UserMatch.class) != null) {
        	if (propertyGetter.getReturnType() != String.class) {
	    		throw new RuntimeException("Annotation 'UserMatch' should be applied to property "
	    				+ "of type 'String'.");
        	}
    		return new PropertyContext<String>(descriptor) {

				@Override
				public PropertyViewer renderForView(String componentId, IModel<String> model) {
					String user = model.getObject();
					if(new Anyone().toString().equals(user)) {
						return new StringPropertyViewer(componentId, descriptor, new ResourceModel("UserMatchEditSupport.value.anyone", user).getObject()); 
					}else{
						return new StringPropertyViewer(componentId, descriptor, user);
					}
				}

				@Override
				public PropertyEditor<String> renderForEdit(String componentId, IModel<String> model) {
		        	return new StringPropertyEditor(componentId, descriptor, model)
		        			.setInputAssist(new UserMatchBehavior());
				}
    			
    		};
        } else {
            return null;
        }
	}

	@Override
	public int getPriority() {
		return DEFAULT_PRIORITY;
	}
	
}
