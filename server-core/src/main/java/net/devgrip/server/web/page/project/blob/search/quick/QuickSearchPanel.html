<wicket:panel>
	<div class="quick-search">
		<div class="modal-header">
			<h5 class="modal-title"><wicket:message key="QuickSearchPanel.title"></wicket:message><span class="text-muted font-size-sm"><wicket:message key="QuickSearchPanel.title2"></wicket:message></span></h5>
			<button wicket:id="close" type="button" class="close"><wicket:svg href="times" class="icon"/></button>
		</div>
		<div class="modal-body">
			<input wicket:id="input" class="form-control"></input>
			<div wicket:id="result" class="result mt-3 overflow-auto">
				<wicket:enclosure child="symbolHits">
				<ul class="list-unstyled mb-0">
					<li wicket:id="symbolHits" class="hit selectable">
						<a wicket:id="link">
							<span class="icon"><img wicket:id="icon"></img></span>
							<span wicket:id="label" class="text"></span>
							<wicket:enclosure child="scope">
								<span class="scope">
								-- <span wicket:id="scope"></span> 
								</span>
							</wicket:enclosure>
						</a>
					</li>
					<wicket:enclosure child="moreSymbolHits">
					<li class="hit selectable more-matches">
						<a wicket:id="moreSymbolHits">
							<wicket:svg href="hand" class="icon"></wicket:svg> <wicket:message key="QuickSearchPanel.showMore"></wicket:message>
						</a>
					</li>
					</wicket:enclosure>
				</ul>
				</wicket:enclosure>
				<div wicket:id="noMatches" class="no-matches alert alert-notice alert-light-warning"><wicket:message key="QuickSearchPanel.noMatch"></wicket:message></div>
			</div>
		</div>
	</div>
</wicket:panel>
