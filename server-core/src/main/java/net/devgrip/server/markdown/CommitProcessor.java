package net.devgrip.server.markdown;

import java.util.Collection;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Nullable;

import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.ProjectManager;
import net.devgrip.server.util.ProjectAndRevision;
import net.devgrip.server.validation.validator.ProjectPathValidator;
import net.devgrip.server.web.UrlManager;
import org.apache.wicket.request.cycle.RequestCycle;
import org.eclipse.jgit.lib.ObjectId;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.NodeTraversor;

import com.google.common.collect.ImmutableSet;

import net.devgrip.server.git.GitUtils;
import net.devgrip.server.model.Project;
import net.devgrip.server.util.HtmlUtils;
import net.devgrip.server.util.TextNodeVisitor;
import net.devgrip.server.web.component.markdown.SuggestionSupport;
import net.devgrip.server.web.page.project.blob.render.BlobRenderContext;
import net.devgrip.server.web.page.project.commits.CommitDetailPage;

public class CommitProcessor implements HtmlProcessor {
	
	private static final Collection<String> IGNORED_TAGS = ImmutableSet.of("pre", "code", "a");

	private static final Pattern PATTERN_COMMIT = Pattern.compile(
			"(^|\\W+)((" + ProjectPathValidator.PATTERN.pattern() + "):)?([a-f0-9]{40})($|[^a-f0-9])");
	
	@Override
	public void process(Document document, @Nullable Project project,
						@Nullable BlobRenderContext blobRenderContext,
						@Nullable SuggestionSupport suggestionSupport,
						boolean forExternal) {
		TextNodeVisitor visitor = new TextNodeVisitor() {
			
			@Override
			protected boolean isApplicable(TextNode node) {
				return !HtmlUtils.hasAncestor(node, IGNORED_TAGS);
			}
		};
		
		NodeTraversor.traverse(visitor, document);
		
		ProjectManager projectManager = AppServer.getInstance(ProjectManager.class);
		for (TextNode node : visitor.getMatchedNodes()) {
			Matcher matcher = PATTERN_COMMIT.matcher(node.getWholeText());
			while (matcher.find()) {
				String commitReplacement;
				String commitProjectPath = matcher.group(3);
				String commitHash = matcher.group(5);
				Project commitProject = project;
				String commitPrefix = "";
				if (commitProjectPath != null) {
					commitProject = projectManager.findByPath(commitProjectPath);
					commitPrefix = commitProjectPath + ":";
				}
				if (commitProject != null) {
					ObjectId commitId = ObjectId.fromString(commitHash);
					if (commitProject.getRevCommit(commitId, false) != null) {
						String url;
						if (RequestCycle.get() != null) 
							url = RequestCycle.get().urlFor(CommitDetailPage.class, CommitDetailPage.paramsOf(commitProject, commitId.name())).toString();
						else 
							url = AppServer.getInstance(UrlManager.class).urlFor(new ProjectAndRevision(commitProject, commitId.name()));
						commitReplacement = String.format("<a href='%s' class='commit reference' data-reference='%s'>%s</a>", 
								url, commitPrefix + commitId.name(), commitPrefix + GitUtils.abbreviateSHA(commitId.name()));
					} else {
						commitReplacement = commitPrefix + commitHash;
					}
				} else {
					commitReplacement = commitPrefix + commitHash; 
				}
				HtmlUtils.appendReplacement(matcher, node, matcher.group(1) + commitReplacement + matcher.group(6));
			}
			HtmlUtils.appendTail(matcher, node);
		}
	}
	
}
