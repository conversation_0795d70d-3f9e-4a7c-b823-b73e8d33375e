package net.devgrip.server.web.component.entity.nav;

import net.devgrip.commons.utils.WordUtils;
import net.devgrip.server.model.AbstractEntity;
import net.devgrip.server.model.Project;
import net.devgrip.server.search.entity.EntityQuery;
import net.devgrip.server.util.ProjectScope;
import net.devgrip.server.util.ReflectionUtils;
import net.devgrip.server.web.util.Cursor;
import net.devgrip.server.web.util.CursorSupport;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.AbstractReadOnlyModel;
import org.apache.wicket.model.StringResourceModel;
import org.apache.wicket.protocol.http.WebSession;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.wicket.model.Model.ofMap;

public abstract class EntityNavPanel<T extends AbstractEntity> extends Panel {

	private final String entityName;
	
	public EntityNavPanel(String id, @Nullable String entityName) {
		super(id);
		
		List<Class<?>> typeArguments = ReflectionUtils.getTypeArguments(EntityNavPanel.class, getClass());
		if (entityName != null)
			this.entityName = entityName;
		else
			this.entityName = WordUtils.uncamel(typeArguments.get(0).getSimpleName()).toLowerCase();
	}

	public EntityNavPanel(String id) {
		this(id, null);
	}
	
	private Cursor getCursor() {
		if (getCursorSupport() != null)
			return getCursorSupport().getCursor();
		else
			return null;
	}

	@Override
	protected void onInitialize() {
		super.onInitialize();
		add(new AjaxLink<Void>("prev") {

			@Override
			protected void onConfigure() {
				super.onConfigure();
				setEnabled(getCursor() != null && getCursor().getOffset() > 0);
			}

			@Override
			protected void onComponentTag(ComponentTag tag) {
				super.onComponentTag(tag);
				if (getCursor() == null || getCursor().getOffset() <= 0)
					tag.put("disabled", "disabled");
				
				Map<String,Object> myParams = new HashMap<>();
				myParams.put("entityName", entityName);
				StringResourceModel stringResourceModel = new StringResourceModel("EntityNavPanel.prevTitle", ofMap(myParams));
				
				tag.put("title", stringResourceModel.getString());
			}

			@Override
			public void onClick(AjaxRequestTarget target) {
				ProjectScope projectScope = getCursor().getProjectScope();
				EntityQuery<T> query = parse(getCursor().getQuery(), 
						projectScope!=null?projectScope.getProject():null);
				int count = getCursor().getCount();
				int offset = getCursor().getOffset() - 1;
				List<T> entities = query(query, offset, 1, projectScope);
				if (!entities.isEmpty()) {
					if (!query.matches(getEntity()))
						count--;
					Cursor prevCursor = new Cursor(getCursor().getQuery(), count, offset, projectScope);
					getCursorSupport().navTo(target, entities.get(0), prevCursor);
				} else {
					Map<String,Object> myParams = new HashMap<>();
					myParams.put("entityName", entityName);
					StringResourceModel stringResourceModel = new StringResourceModel("EntityNavPanel.noMore", ofMap(myParams));
					WebSession.get().warn(stringResourceModel.getString());
				}
			}
			
		});
		add(new AjaxLink<Void>("next") {

			@Override
			protected void onConfigure() {
				super.onConfigure();
				setEnabled(getCursor() != null && getCursor().getOffset()<getCursor().getCount()-1);
			}

			@Override
			protected void onComponentTag(ComponentTag tag) {
				super.onComponentTag(tag);
				if (getCursor() == null || getCursor().getOffset() >= getCursor().getCount()-1)
					tag.put("disabled", "disabled");

				Map<String,Object> myParams = new HashMap<>();
				myParams.put("entityName", entityName);
				StringResourceModel stringResourceModel = new StringResourceModel("EntityNavPanel.nextTitle", ofMap(myParams));
				tag.put("title", stringResourceModel.getString());
			}

			@Override
			public void onClick(AjaxRequestTarget target) {
				ProjectScope projectScope = getCursor().getProjectScope();
				EntityQuery<T> query = parse(getCursor().getQuery(), projectScope!=null?projectScope.getProject():null);
				int offset = getCursor().getOffset();
				int count = getCursor().getCount();
				if (query.matches(getEntity())) 
					offset++;
				else
					count--;
				
				List<T> entities = query(query, offset, 1, projectScope);
				if (!entities.isEmpty()) {
					Cursor nextCursor = new Cursor(getCursor().getQuery(), count, offset, projectScope);
					getCursorSupport().navTo(target, entities.get(0), nextCursor);
				} else {
					Map<String,Object> myParams = new HashMap<>();
					myParams.put("entityName", entityName);
					StringResourceModel stringResourceModel = new StringResourceModel("EntityNavPanel.noMore", ofMap(myParams));
					WebSession.get().warn(stringResourceModel.getString());
				}
			}
			
		});
		
		add(new Label("current", new AbstractReadOnlyModel<String>() {

			@Override
			public String getObject() {
				long total = 1L;
				long offset = 1L;
				Map<String,Object> myParams = new HashMap<>();
				myParams.put("entityName", entityName);
				myParams.put("total", total);
				myParams.put("offset", offset);
				if (getCursor() != null) {
					myParams.put("total", getCursor().getCount());
					myParams.put("offset", getCursor().getOffset() + 1);
				}
				StringResourceModel stringResourceModel = new StringResourceModel("EntityNavPanel.entityCountLabel", ofMap(myParams));
				return stringResourceModel.getString();
			}
			
		}));
	}

	protected abstract EntityQuery<T> parse(String queryString, @Nullable Project project);
	
	protected abstract T getEntity();
	
	@Nullable
	protected abstract CursorSupport<T> getCursorSupport();
	
	protected abstract List<T> query(EntityQuery<T> query, int offset, int count, @Nullable ProjectScope projectScope);
	
}
