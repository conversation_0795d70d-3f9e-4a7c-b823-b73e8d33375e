package net.devgrip.server.x.highsearch.page;

import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import net.devgrip.commons.utils.ExceptionUtils;
import net.devgrip.server.AppServer;
import net.devgrip.server.cluster.ClusterManager;
import net.devgrip.server.entitymanager.ProjectManager;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.git.BlobIdent;
import net.devgrip.server.git.GitUtils;
import net.devgrip.server.model.Project;
import net.devgrip.server.search.code.query.FileQueryOption;
import net.devgrip.server.search.code.query.QueryOption;
import net.devgrip.server.search.code.query.SymbolQueryOption;
import net.devgrip.server.search.code.query.TextQueryOption;
import net.devgrip.server.util.ReflectionUtils;
import net.devgrip.server.web.WebConstants;
import net.devgrip.server.web.WebSession;
import net.devgrip.server.web.behavior.ChangeObserver;
import net.devgrip.server.web.behavior.RunTaskBehavior;
import net.devgrip.server.web.component.project.ProjectAvatar;
import net.devgrip.server.web.editable.BeanContext;
import net.devgrip.server.web.page.layout.TopbarOnlyLayoutPage;
import net.devgrip.server.web.page.project.blob.ProjectBlobPage;
import net.devgrip.server.web.page.project.dashboard.ProjectDashboardPage;
import net.devgrip.server.x.highsearch.HsIndexManager;
import net.devgrip.server.x.highsearch.HsIndexStatusChanged;
import net.devgrip.server.x.highsearch.ProjectsBean;
import net.devgrip.server.x.highsearch.SearchManager;
import net.devgrip.server.x.highsearch.hit.HitContent;
import net.devgrip.server.x.highsearch.hit.Hit;
import net.devgrip.server.x.highsearch.searchsupport.SearchBase;
import net.devgrip.server.x.highsearch.searchsupport.SearchFile;
import net.devgrip.server.x.highsearch.searchsupport.SearchSymbol;
import net.devgrip.server.x.highsearch.searchsupport.SearchText.Builder;
import org.apache.wicket.Component;
import org.apache.wicket.MetaDataKey;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.core.request.handler.IPartialPageRequestHandler;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.markup.html.panel.Fragment;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.AbstractReadOnlyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.io.Serializable;
import java.util.*;

/**
 *
 */

public abstract class HsPage<T extends Serializable> extends TopbarOnlyLayoutPage {
	private static final String CODE_SEARCH_TITLE = "codeSearchTitle";
	private static final MetaDataKey<HashMap<Class<?>, QueryOption>> QUERY_OPTIONS = new MetaDataKey<>() {};
	private static final MetaDataKey<String> PROJECTS = new MetaDataKey<>() {};
	private QueryOption option;
	private String projects;
	private int page = 1;

	public HsPage(PageParameters params) {
		super(params);
		List<Class<?>> typeArguments = ReflectionUtils.getTypeArguments(HsPage.class, this.getClass());

		try {
			this.option = (QueryOption)typeArguments.get(0).getDeclaredConstructor().newInstance();
		} catch (Exception var5) {
			throw ExceptionUtils.unchecked(var5);
		}

		Map<Class<?>, QueryOption> savedOptions = this.getSavedOptions();
		if (savedOptions.containsKey(this.option.getClass())) {
			this.option = savedOptions.get(this.option.getClass());
		}

		String projects = WebSession.get().getMetaData(PROJECTS);
		if (projects != null) {
			this.projects = projects;
		}
	}

	protected void onInitialize() {
		super.onInitialize();

		add(newTopbarTitle(CODE_SEARCH_TITLE));
		
		final ProjectsBean projectsBean = new ProjectsBean();
		projectsBean.setProjects(this.projects);
		Form<Void> form = new Form<>("form");
		final FormComponent<? extends QueryOption> optionEditor = this.option.newOptionEditor("option");
		form.add(optionEditor);
		form.add(BeanContext.edit("projects", projectsBean));
		WebMarkupContainer foot = new WebMarkupContainer("foot");
		form.add(foot.setOutputMarkupId(true));
		foot.add(new AjaxButton("search") {
			private RunTaskBehavior runTaskBehavior;

			@Override
			protected void onInitialize() {
				super.onInitialize();
				this.add(this.runTaskBehavior = new RunTaskBehavior() {
					protected void runTask(AjaxRequestTarget target) {
						HsPage.this.page = 1;
						List<HitContent> hitContents = HsPage.this.search();
						if (!hitContents.isEmpty()) {
							Fragment hasMatchesFrag = new Fragment("result", "hasMatchesFrag", HsPage.this);
							final RepeatingView blobMatchesView = new RepeatingView("blobMatches");
							boolean firstItem = true;

							for (HitContent hitContent : hitContents) {
								blobMatchesView.add(HsPage.this.newBlobMatchItem(blobMatchesView.newChildId(), hitContent, firstItem));
								firstItem = false;
							}

							hasMatchesFrag.add(blobMatchesView);
							hasMatchesFrag.add((new AjaxLink<Void>("more") {
								private RunTaskBehavior runTaskBehavior;

								@Override
								protected void onInitialize() {
									super.onInitialize();
									final AjaxLink<Void> moreLink = this;
									this.add(this.runTaskBehavior = new RunTaskBehavior() {
										protected void runTask(AjaxRequestTarget target) {
											HsPage.this.page++;
											List<HitContent> hitContents = HsPage.this.search();
											boolean firstItem = true;

											for (HitContent hitContent : hitContents) {
												Component item = HsPage.this.newBlobMatchItem(blobMatchesView.newChildId(), hitContent, firstItem);
												blobMatchesView.add(item);
												String script = String.format("$('.blob-matches').append('<li id=\"%s\"></li>');", item.getMarkupId());
												target.prependJavaScript(script);
												target.add(item);
												firstItem = false;
											}

											setVisible(hitContents.size() == WebConstants.PAGE_SIZE);
											target.add(moreLink);
										}
									});
								}

								@Override
								public void onClick(AjaxRequestTarget target) {
									this.runTaskBehavior.requestRun(target);
									target.focusComponent(null);
								}
							}).setVisible(hitContents.size() == WebConstants.PAGE_SIZE));
							hasMatchesFrag.setOutputMarkupId(true);
							HsPage.this.replace(hasMatchesFrag);
							target.add(hasMatchesFrag);
						} else {
							Fragment noMatchesFrag = new Fragment("result", "noMatchesFrag", HsPage.this);
							noMatchesFrag.setOutputMarkupId(true);
							HsPage.this.replace(noMatchesFrag);
							target.add(noMatchesFrag);
						}
					}
				});
			}

			@Override
			protected void onSubmit(AjaxRequestTarget target, Form<?> form) {
				super.onSubmit(target, form);
				HsPage.this.option = optionEditor.getModelObject();
				HsPage.this.projects = projectsBean.getProjects();
				HashMap<Class<?>, QueryOption> savedOptions = HsPage.this.getSavedOptions();
				savedOptions.put(HsPage.this.option.getClass(), HsPage.this.option);
				WebSession.get().setMetaData(HsPage.QUERY_OPTIONS, savedOptions);
				WebSession.get().setMetaData(HsPage.PROJECTS, HsPage.this.projects);
				target.add(form);
				this.runTaskBehavior.requestRun(target);
			}

			@Override
			protected void onError(AjaxRequestTarget target, Form<?> form) {
				super.onError(target, form);
				target.add(form);
			}
		});
		final var indexing = new WebMarkupContainer("indexing") {
			@Override
			protected void onConfigure() {
				super.onConfigure();
				this.setVisible(
					AppServer.getInstance(ClusterManager.class)
                             .runOnAllServers(() -> AppServer.getInstance(HsIndexManager.class).isIndexing())
                             .entrySet()
                             .stream()
                             .anyMatch(Map.Entry::getValue)
				);
			}
		};
		foot.add(indexing.setOutputMarkupPlaceholderTag(true));
		foot.add(new ChangeObserver() {
			public void onObservableChanged(IPartialPageRequestHandler handler, Collection<String> observables) {
				handler.add(indexing);
			}

			protected Collection<String> findObservables() {
				return Sets.newHashSet(HsIndexStatusChanged.getChangeObservable());
			}
		});
		this.add(form);
		this.add(new WebMarkupContainer("result").setOutputMarkupPlaceholderTag(true).setVisible(false));
	}

	private List<HitContent> search() {
		int count = this.page * WebConstants.PAGE_SIZE;
		SearchManager searchManager = AppServer.getInstance(SearchManager.class);
		List<HitContent> hitContents;
		if (this.option instanceof TextQueryOption) {
			SearchBase query = new Builder((TextQueryOption)this.option).projects(this.projects).count(count).build();
			hitContents = searchManager.search(query);
		} else if (this.option instanceof FileQueryOption) {
			SearchBase query = new SearchFile.Builder((FileQueryOption)this.option)
				.projects(this.projects)
				.count(count)
				.build();
			hitContents = searchManager.search(query);
		} else {
			SearchBase query = new SearchSymbol.Builder((SymbolQueryOption)this.option)
				.primary(true)
				.projects(this.projects)
				.count(count)
				.build();
			hitContents = searchManager.search(query);
			if (hitContents.size() < count) {
				query = new SearchSymbol.Builder((SymbolQueryOption)this.option)
					.primary(false)
					.projects(this.projects)
					.count(count - hitContents.size())
					.build();
				hitContents.addAll(searchManager.search(query));
			}
		}

		int newIndex = (this.page - 1) * WebConstants.PAGE_SIZE;
		return newIndex < hitContents.size() ? hitContents.subList(newIndex, hitContents.size()) : new ArrayList<>();
	}

	private Component newBlobMatchItem(String componentId, HitContent hitContent, boolean firstItem) {
		final WebMarkupContainer item = new WebMarkupContainer(componentId);
		final Long projectId = hitContent.getProjectId();
		Project project = this.getProjectManager().load(projectId);
		BookmarkablePageLink<Void> projectLink = new BookmarkablePageLink<>("project", ProjectDashboardPage.class, ProjectDashboardPage.paramsOf(projectId));
		projectLink.add(new ProjectAvatar("avatar", projectId));
		projectLink.add(new Label("label", project.getPath()));
		item.add(projectLink);
		String revision = Preconditions.checkNotNull(project.getDefaultBranch());
		if (project.getTagRef(revision) != null) {
			revision = GitUtils.branch2ref(revision);
		}

		final BlobIdent blobIdent = new BlobIdent(revision, hitContent.getBlobPath());
		BookmarkablePageLink<Void> blobLink = new BookmarkablePageLink<>("blob", ProjectBlobPage.class, ProjectBlobPage.paramsOf(project, blobIdent));
		blobLink.add(new Label("label", hitContent.getBlobPath()));
		item.add(blobLink);
		final var contentMatchesView = new ListView<>("contentMatches", hitContent.getContentMatches()) {
			@Override
			protected void populateItem(ListItem<Hit> item) {
				Hit hit = item.getModelObject();
				Project project = HsPage.this.getProjectManager().load(projectId);
				ProjectBlobPage.State state = new ProjectBlobPage.State();
				state.blobIdent = new BlobIdent(blobIdent.revision, blobIdent.path);
				state.position = hit.getStatePos();
				BookmarkablePageLink<Object> link = new BookmarkablePageLink<>("link", ProjectBlobPage.class, ProjectBlobPage.paramsOf(project, state));
				link.add(hit.getLineContent("lineNo"));
				link.add(hit.getContentComponent("content"));
				item.add(link);
			}
		};
		contentMatchesView.setVisible(firstItem);
		item.add(contentMatchesView);
		item.add((new AjaxLink<>("expand") {
			@Override
			protected void onInitialize() {
				super.onInitialize();
				this.add(AttributeAppender.append("class", new AbstractReadOnlyModel<String>() {
					public String getObject() {
						return contentMatchesView.isVisible() ? "expanded" : "";
					}
				}));
			}

			@Override
			public void onClick(AjaxRequestTarget target) {
				contentMatchesView.setVisible(!contentMatchesView.isVisible());
				target.add(item);
			}
		}).setVisible(!hitContent.getContentMatches().isEmpty()));
		item.setOutputMarkupId(true);
		return item;
	}

	private ProjectManager getProjectManager() {
		return AppServer.getInstance(ProjectManager.class);
	}

	private HashMap<Class<?>, QueryOption> getSavedOptions() {
		HashMap<Class<?>, QueryOption> savedOptions = WebSession.get().getMetaData(QUERY_OPTIONS);
		if (savedOptions == null) {
			savedOptions = new HashMap<>();
		}

		return savedOptions;
	}

	public void renderHead(IHeaderResponse response) {
		super.renderHead(response);
		response.render(CssHeaderItem.forReference(new HsCssResourceReference()));
	}

	@Override
	protected String getPageTitle() {
		return getString("Code_Search") + "-" + AppServer.getInstance(SettingManager.class).getBrandingSetting().getName();
	}
}
