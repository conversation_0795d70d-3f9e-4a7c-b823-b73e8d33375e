package net.devgrip.server.web.component.pullrequest.review;

import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.PullRequestReviewManager;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequestReview;
import net.devgrip.server.model.PullRequestReview.Status;
import net.devgrip.server.model.User;
import net.devgrip.server.web.component.select2.SelectToActChoice;
import net.devgrip.server.web.component.user.choice.UserChoiceResourceReference;
import net.devgrip.server.web.page.base.BasePage;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.model.ResourceModel;

public abstract class ReviewerChoice extends SelectToActChoice<User> {

	public ReviewerChoice(String id) {
		super(id);
		
		setProvider(new ReviewerProvider() {

			@Override
			protected PullRequest getPullRequest() {
				return ReviewerChoice.this.getPullRequest();
			}
			
		});
	}

	@Override
	protected void onInitialize() {
		super.onInitialize();
		
		getSettings().setPlaceholder(new ResourceModel("ReviewerChoice.addReviewer.placeholder").getObject());
		getSettings().setFormatResult("onedev.server.userChoiceFormatter.formatResult");
		getSettings().setFormatSelection("onedev.server.userChoiceFormatter.formatSelection");
		getSettings().setEscapeMarkup("onedev.server.userChoiceFormatter.escapeMarkup");
	}

	@Override
	public void renderHead(IHeaderResponse response) {
		super.renderHead(response);
		
		response.render(JavaScriptHeaderItem.forReference(new UserChoiceResourceReference()));
	}

	protected abstract PullRequest getPullRequest();

	@Override
	protected void onSelect(AjaxRequestTarget target, User user) {
		PullRequestReview review = getPullRequest().getReview(user);
		if (review == null) {
			review = new PullRequestReview();
			review.setRequest(getPullRequest());
			review.setUser(user);
			getPullRequest().getReviews().add(review);
		} else {
			review.setStatus(Status.PENDING);
		}
		
		if (!getPullRequest().isNew()) {
			AppServer.getInstance(PullRequestReviewManager.class).createOrUpdate(review);
			((BasePage)getPage()).notifyObservableChange(target,
					PullRequest.getChangeObservable(getPullRequest().getId()));
		}
	};
	
}
