package net.devgrip.server.web.editable.buildspec.job.postbuildaction;

import java.util.List;

import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.request.cycle.RequestCycle;

import net.devgrip.server.buildspec.BuildSpecAware;
import net.devgrip.server.buildspec.job.JobAware;
import net.devgrip.server.buildspec.job.action.PostBuildAction;
import net.devgrip.server.web.ajaxlistener.ConfirmLeaveListener;
import net.devgrip.server.web.editable.BeanContext;
import net.devgrip.server.web.editable.BeanEditor;

public abstract class PostBuildActionEditPanel extends Panel implements BuildSpecAware, JobAware {

	private final List<PostBuildAction> actions;
	
	private final int actionIndex;
	
	public PostBuildActionEditPanel(String id, List<PostBuildAction> actions, int actionIndex) {
		super(id);
	
		this.actions = actions;
		this.actionIndex = actionIndex;
	}
	
	@Override
	protected void onInitialize() {
		super.onInitialize();
		
		PostBuildActionBean bean = new PostBuildActionBean();
		if (actionIndex != -1)
			bean.setAction(actions.get(actionIndex));

		Form<?> form = new Form<Void>("form") {

			@Override
			protected void onError() {
				super.onError();
				RequestCycle.get().find(AjaxRequestTarget.class).add(this);
			}
			
		};
		
		form.add(new AjaxLink<Void>("close") {

			@Override
			protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
				super.updateAjaxAttributes(attributes);
				attributes.getAjaxCallListeners().add(new ConfirmLeaveListener(PostBuildActionEditPanel.this));
			}

			@Override
			public void onClick(AjaxRequestTarget target) {
				onCancel(target);
			}
			
		});
		
		BeanEditor editor = BeanContext.edit("editor", bean);
		form.add(editor);
		form.add(new AjaxButton("save") {

			@Override
			protected void onSubmit(AjaxRequestTarget target, Form<?> form) {
				super.onSubmit(target, form);

				PostBuildAction action = bean.getAction();
				if (editor.isValid()) {
					if (actionIndex != -1) 
						actions.set(actionIndex, action);
					else 
						actions.add(action);
					onSave(target);
				} else {
					target.add(form);
				}
			}
			
		});
		
		form.add(new AjaxLink<Void>("cancel") {

			@Override
			protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
				super.updateAjaxAttributes(attributes);
				attributes.getAjaxCallListeners().add(new ConfirmLeaveListener(PostBuildActionEditPanel.this));
			}

			@Override
			public void onClick(AjaxRequestTarget target) {
				onCancel(target);
			}
			
		});
		form.setOutputMarkupId(true);
		
		add(form);
	}

	protected abstract void onSave(AjaxRequestTarget target);
	
	protected abstract void onCancel(AjaxRequestTarget target);

}
