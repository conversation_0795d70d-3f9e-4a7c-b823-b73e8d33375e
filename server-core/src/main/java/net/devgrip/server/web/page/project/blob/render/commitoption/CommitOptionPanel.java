package net.devgrip.server.web.page.project.blob.render.commitoption;

import com.google.common.base.Objects;
import com.google.common.base.Preconditions;
import net.devgrip.commons.utils.ExceptionUtils;
import net.devgrip.server.AppServer;
import net.devgrip.server.git.*;
import net.devgrip.server.git.exception.NotTreeException;
import net.devgrip.server.git.exception.ObjectAlreadyExistsException;
import net.devgrip.server.git.exception.ObsoleteCommitException;
import net.devgrip.server.git.service.GitService;
import net.devgrip.server.git.service.PathChange;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.User;
import net.devgrip.server.security.SecurityUtils;
import net.devgrip.server.util.Provider;
import net.devgrip.server.util.diff.WhitespaceOption;
import net.devgrip.server.web.ajaxlistener.ConfirmLeaveListener;
import net.devgrip.server.web.ajaxlistener.TrackViewStateListener;
import net.devgrip.server.web.component.diff.blob.BlobDiffPanel;
import net.devgrip.server.web.component.diff.revision.DiffViewMode;
import net.devgrip.server.web.component.link.ViewStateAwareAjaxLink;
import net.devgrip.server.web.editable.BeanContext;
import net.devgrip.server.web.page.project.blob.RevisionResolved;
import net.devgrip.server.web.page.project.blob.navigator.BlobNameChanging;
import net.devgrip.server.web.page.project.blob.render.BlobRenderContext;
import net.devgrip.server.web.page.project.blob.render.BlobRenderContext.Mode;
import org.apache.commons.lang3.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes.Method;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.core.request.handler.IPartialPageRequestHandler;
import org.apache.wicket.event.Broadcast;
import org.apache.wicket.event.IEvent;
import org.apache.wicket.feedback.FencedFeedbackPanel;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.ResourceModel;
import org.eclipse.jgit.diff.DiffEntry;
import org.eclipse.jgit.lib.FileMode;
import org.eclipse.jgit.lib.ObjectId;

import javax.annotation.Nullable;
import java.util.*;

public class CommitOptionPanel extends Panel {

	private final BlobRenderContext context;
	
	private final Provider<byte[]> newContentProvider;
	
	private CommitMessageBean commitMessageBean = new CommitMessageBean();
	
	private BlobChange changesOfOthers;
	
	private boolean contentModified;
	
	private Set<String> oldPaths;
	
	private Form<?> form;
	
	public CommitOptionPanel(String id, BlobRenderContext context, @Nullable Provider<byte[]> newContentProvider) {
		super(id);

		this.context = context;
		this.newContentProvider = newContentProvider;

		oldPaths = new HashSet<>();
		String oldPath = getOldPath();
		if (oldPath != null)
			oldPaths.add(oldPath);
	}

	@Nullable
	private String getOldPath() {
		return context.getMode()!=Mode.ADD? context.getBlobIdent().path: null;
	}
	
	public String getDefaultCommitMessage() {
		String oldPath = getOldPath();
		String oldName;
		Map<String, Object> msgParams = new HashMap<>();
		if (oldPath != null && oldPath.contains("/"))
			oldName = StringUtils.substringAfterLast(oldPath, "/");
		else
			oldName = oldPath;
		
		msgParams.put("oldName", oldName);
		String commitMessage;
		if (newContentProvider == null) {
			commitMessage = getString("CommitOptionPanel.commitMsgDelete", Model.ofMap(msgParams));
		} else {
			String newPath = context.getNewPath();

			String newName;
			if (newPath != null && newPath.contains("/"))
				newName = StringUtils.substringAfterLast(newPath, "/");
			else
				newName = newPath;
			
			if (oldPath == null) {
				if (newName != null) {
					msgParams.put("newName", newName);
					commitMessage = getString("CommitOptionPanel.commitMsgAdd1", Model.ofMap(msgParams));
				} else {
					commitMessage = getString("CommitOptionPanel.commitMsgAdd2");
				}
			} else if (oldPath.equals(newPath)) {
				 commitMessage = getString("CommitOptionPanel.commitMsgEdit", Model.ofMap(msgParams));
			} else {
				 commitMessage = getString("CommitOptionPanel.commitMsgRename", Model.ofMap(msgParams));
			}
		}
		if (context.getProject().getBranchProtection(context.getBlobIdent().revision, SecurityUtils.getUser()).isEnforceConventionalCommits())
			commitMessage = "chore: " + commitMessage;
		return commitMessage;
			
	}
	
	private GitService getGitService() {
		return AppServer.getInstance(GitService.class);
	}
	
	private void newChangesOfOthersContainer(@Nullable AjaxRequestTarget target) {
		Component changesOfOthersContainer;
		if (changesOfOthers != null) 
			changesOfOthersContainer = new BlobDiffPanel("changesOfOthers", changesOfOthers, DiffViewMode.UNIFIED, null);
		else 
			changesOfOthersContainer = new WebMarkupContainer("changesOfOthers").setVisible(false);
		if (target != null) {
			form.replace(changesOfOthersContainer);
			target.add(form);
			if (changesOfOthers != null) {
				String script = String.format("$('#%s .commit-option input[type=submit]').val('Commit and overwrite');", 
						getMarkupId());
				target.appendJavaScript(script);
			}
		} else {
			form.add(changesOfOthersContainer);		
		}
	}
	
	@Override
	protected void onInitialize() {
		super.onInitialize();
		
		form = new Form<Void>("form");
		form.setOutputMarkupId(true);
		add(form);

		form.add(new FencedFeedbackPanel("feedback", form));
		newChangesOfOthersContainer(null);
		form.add(BeanContext.edit("commitMessage", commitMessageBean));
		String pleaseWait = getString("Please_Wait");
		AjaxButton saveButton = new AjaxButton("save") {

			@Override
			protected void onError(AjaxRequestTarget target, Form<?> form) {
				super.onError(target, form);
				target.add(form);
			}

			@Override
			protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
				super.updateAjaxAttributes(attributes);
				attributes.setMethod(Method.POST);

				attributes.getAjaxCallListeners().add(new TrackViewStateListener(true));
			}

			@Override
			protected void onComponentTag(ComponentTag tag) {
				super.onComponentTag(tag);

				if (!isBlobModified())
					tag.put("disabled", "disabled");
			}

			@Override
			protected void onSubmit(AjaxRequestTarget target, Form<?> form) {
				super.onSubmit(target, form);
				
				if (save(target)) {
					String script = String.format(""
							+ "$('#%s').attr('disabled', 'disabled').val('%s');"
							+ "onedev.server.form.markClean($('form'));", getMarkupId(), pleaseWait);
					target.appendJavaScript(script);
				} 
			}
			
		};
		saveButton.setOutputMarkupId(true);
		form.add(saveButton);
		
		form.add(new ViewStateAwareAjaxLink<Void>("cancel", true) {

			@Override
			protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
				super.updateAjaxAttributes(attributes);
				attributes.getAjaxCallListeners().add(new ConfirmLeaveListener());
			}

			@Override
			protected void onConfigure() {
				super.onConfigure();
				setVisible(newContentProvider == null);
			}

			@Override
			public void onClick(AjaxRequestTarget target) {
				context.onModeChange(target, Mode.VIEW, null);
			}
			
		});

		setOutputMarkupId(true);
	}
	
	private boolean isBlobModified() {
		return !context.getBlobIdent().isFile() 
				|| context.getMode() == Mode.DELETE
				|| contentModified 
				|| !Objects.equal(context.getBlobIdent().path, context.getNewPath());
	}
	
	private boolean save(AjaxRequestTarget target) {
		changesOfOthers = null;
		
		if (newContentProvider != null && StringUtils.isBlank(context.getNewPath())) {
			String err = new ResourceModel("CommitOptionPanel.error.filenameNeed").getObject();
			form.error(err);
			target.add(form);
			return false;
		} else {
			User user = SecurityUtils.getAuthUser();
			String commitMessage = commitMessageBean.getCommitMessage();
			if (commitMessage == null)
				commitMessage = getDefaultCommitMessage();
			var branchProtection = context.getProject().getBranchProtection(context.getBlobIdent().revision, user);
			var errorMessage = branchProtection.checkCommitMessage(commitMessage, false);
			if (errorMessage != null) {
				form.error(errorMessage);
				target.add(form);
				return false;
			}
			String revision = context.getBlobIdent().revision;
			ObjectId prevCommitId;
			if (revision != null)
				prevCommitId = context.getProject().getObjectId(revision, true);
			else
				prevCommitId = ObjectId.zeroId();

			if (revision == null)
				revision = "main";
			
			String refName = GitUtils.branch2ref(revision);
			
			ObjectId newCommitId = null;
			while(newCommitId == null) {
				try {
					Map<String, BlobContent> newBlobs = new HashMap<>();
					if (newContentProvider != null) {
						String newPath = context.getNewPath();
						if (context.getProject().isReviewRequiredForModification(user, revision, newPath)) {
							String err = new ResourceModel("CommitOptionPanel.error.reviewNeed").getObject();
							form.error(err);
							target.add(form);
							return false;
						} else if (context.getProject().isBuildRequiredForModification(user, revision, newPath)) {
							String err = new ResourceModel("CommitOptionPanel.error.buildNeed").getObject();
							form.error(err);
							target.add(form);
							return false;
						} else if (context.getProject().isCommitSignatureRequiredButNoSigningKey(user, revision)) {
							String err = new ResourceModel("CommitOptionPanel.error.signNeed").getObject();
							form.error(err);
							target.add(form);
							return false;
						}
						
						int mode;
						if (context.getBlobIdent().isFile())
							mode = context.getBlobIdent().mode;
						else
							mode = FileMode.REGULAR_FILE.getBits();
						newBlobs.put(context.getNewPath(), new BlobContent(newContentProvider.get(), mode));
					}

					newCommitId = getGitService().commit(context.getProject(), 
							new BlobEdits(oldPaths, newBlobs), refName, prevCommitId, prevCommitId,
							user.asPerson(), commitMessage, false);
				} catch (Exception e) {
					ObjectAlreadyExistsException objectAlreadyExistsException = 
							ExceptionUtils.find(e, ObjectAlreadyExistsException.class);
					NotTreeException notTreeException = ExceptionUtils.find(e, NotTreeException.class);
					ObsoleteCommitException obsoleteCommitException = 
							ExceptionUtils.find(e, ObsoleteCommitException.class);
					
					if (objectAlreadyExistsException != null) {
						String err = new ResourceModel("CommitOptionPanel.error.nameAlreadyUsed").getObject();
						form.error(err);
						target.add(form);
						break;
					} else if (notTreeException != null) {
						String err = new ResourceModel("CommitOptionPanel.error.fileExists").getObject();
						form.error(err);
						target.add(form);
						break;
					} else if (obsoleteCommitException != null) {
						send(this, Broadcast.BUBBLE, new RevisionResolved(target, obsoleteCommitException.getOldCommitId()));
						ObjectId lastPrevCommitId = prevCommitId;
						prevCommitId = obsoleteCommitException.getOldCommitId();
						if (!oldPaths.isEmpty()) {
							String path = oldPaths.iterator().next();
							PathChange pathChange = getGitService().getPathChange(context.getProject(), 
									lastPrevCommitId, prevCommitId, path);
							Preconditions.checkNotNull(pathChange);
							if (!pathChange.getOldObjectId().equals(pathChange.getNewObjectId()) 
									|| pathChange.getOldMode() != pathChange.getNewMode()) {
								// mark changed if original file exists and content or mode has been modified
								// by others
								if (pathChange.getNewObjectId().equals(ObjectId.zeroId())) {
									if (newContentProvider != null) {
										oldPaths.clear();
										changesOfOthers = getBlobChange(path, pathChange, lastPrevCommitId, prevCommitId);
										String warn = new ResourceModel("CommitOptionPanel.warn.someoneChanged").getObject();
										form.warn(warn);
										break;
									} else {
										newCommitId = obsoleteCommitException.getOldCommitId();
									}
								} else {
									changesOfOthers = getBlobChange(path, pathChange, lastPrevCommitId, prevCommitId);
									String warn = new ResourceModel("CommitOptionPanel.warn.someoneChanged").getObject();
									form.warn(warn);
									break;
								}
							} 
						}
					} else {
						throw ExceptionUtils.unchecked(e);
					}
				}
			}
			if (newCommitId != null) {
				context.onCommitted(target, newCommitId);
				target.appendJavaScript("$(window).resize();");
				return true;
			} else {
				newChangesOfOthersContainer(target);
				return false;
			}
		}
	}
	
	private BlobChange getBlobChange(String path, PathChange pathChange, 
			ObjectId oldCommitId, ObjectId newCommitId) {
		DiffEntry.ChangeType changeType = DiffEntry.ChangeType.MODIFY;
		BlobIdent oldBlobIdent;
		if (!pathChange.getOldObjectId().equals(ObjectId.zeroId())) {
			oldBlobIdent = new BlobIdent(oldCommitId.name(), path, pathChange.getOldMode());
		} else {
			oldBlobIdent = new BlobIdent(oldCommitId.name(), null, FileMode.TREE.getBits());
			changeType = DiffEntry.ChangeType.ADD;
		}
		
		BlobIdent newBlobIdent;
		if (!pathChange.getNewObjectId().equals(ObjectId.zeroId())) {
			newBlobIdent = new BlobIdent(newCommitId.name(), path, pathChange.getNewMode());
		} else {
			newBlobIdent = new BlobIdent(newCommitId.name(), null, FileMode.TREE.getBits());
			changeType = DiffEntry.ChangeType.DELETE;
		}
		
		return new BlobChange(changeType, oldBlobIdent, newBlobIdent, WhitespaceOption.IGNORE_TRAILING) {

			@Override
			public Project getProject() {
				return context.getProject();
			}

		};
	}

	@Override
	public void renderHead(IHeaderResponse response) {
		super.renderHead(response);
		response.render(JavaScriptHeaderItem.forReference(new CommitOptionResourceReference()));
	}
	
	public void onContentChange(IPartialPageRequestHandler partialPageRequestHandler) {
		Preconditions.checkNotNull(newContentProvider);
		
		if (context.getMode() == Mode.EDIT) {
			contentModified = !Arrays.equals(
					newContentProvider.get(), 
					context.getProject().getBlob(context.getBlobIdent(), true).getBytes());
		} else {
			contentModified = newContentProvider.get().length != 0;
		}
		onBlobChange(partialPageRequestHandler);
	}
	
	@Override
	public void onEvent(IEvent<?> event) {
		super.onEvent(event);
		if (event.getPayload() instanceof BlobNameChanging) {
			BlobNameChanging payload = (BlobNameChanging) event.getPayload();
			onBlobChange(payload.getHandler());
		}
	}

	private void onBlobChange(IPartialPageRequestHandler partialPageRequestHandler) {
		String script = String.format("onedev.server.commitOption.onBlobChange('%s', %b);", 
				getMarkupId(), isBlobModified());
		partialPageRequestHandler.appendJavaScript(script);
	}
	
}
