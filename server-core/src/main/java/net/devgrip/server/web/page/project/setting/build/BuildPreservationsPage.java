package net.devgrip.server.web.page.project.setting.build;

import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.ProjectManager;
import net.devgrip.server.web.editable.PropertyContext;
import net.devgrip.server.web.editable.PropertyEditor;
import org.apache.wicket.Component;
import org.apache.wicket.feedback.FencedFeedbackPanel;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.ResourceModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.io.Serializable;

public class BuildPreservationsPage extends ProjectBuildSettingPage {

	public BuildPreservationsPage(PageParameters params) {
		super(params);
	}

	@Override
	protected void onInitialize() {
		super.onInitialize();
		
		BuildPreservationsBean bean = new BuildPreservationsBean();
		bean.setBuildPreservations(getProject().getBuildSetting().getBuildPreservations());
		
		PropertyEditor<Serializable> editor = PropertyContext.edit("editor", bean, "buildPreservations");
		
		Form<?> form = new Form<Void>("form") {

			@Override
			protected void onSubmit() {
				super.onSubmit();
				getSession().success(new ResourceModel("BuildPreservationPage.submitSuccess", "Build preserve rules saved").getObject());
				getProject().getBuildSetting().setBuildPreservations(bean.getBuildPreservations());
				AppServer.getInstance(ProjectManager.class).update(getProject());
				setResponsePage(BuildPreservationsPage.class, 
						BuildPreservationsPage.paramsOf(getProject()));
			}
			
		};
		form.add(new FencedFeedbackPanel("feedback", form));
		form.add(editor);
		add(form);
	}

	@Override
	protected Component newProjectTitle(String componentId) {
		return new Label(componentId, new ResourceModel("Build_Preserve_Rules"));
	}

}
