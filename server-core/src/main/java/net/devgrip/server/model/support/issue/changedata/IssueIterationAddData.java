package net.devgrip.server.model.support.issue.changedata;

import net.devgrip.server.model.Group;
import net.devgrip.server.model.User;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

public class IssueIterationAddData extends IssueChangeData {

	private static final long serialVersionUID = 1L;

	private final String iteration;
	
	public IssueIterationAddData(String iteration) {
		this.iteration = iteration;
	}
	
	@Override
	public String getActivity() {
		return "added to iteration \"" + iteration + "\"";
	}

	public String getIteration() {
		return iteration;
	}

	/**
	 * 获取动态的I18n的key，用于国际化显示
	 *
	 * @return
	 */
	@Override
	public String getActivityI18nKey() {
		return "IssueChangeData.activities.addedToIteration";
	}

	@Override
	public Map<String, Collection<User>> getNewUsers() {
		return new HashMap<>();
	}

	@Override
	public Map<String, Group> getNewGroups() {
		return new HashMap<>();
	}

	@Override
	public boolean affectsListing() {
		return true;
	}

}
