<%
    if (htmlVersion) {
        print """
            <b>${eventSummary}</b>
            <br>
            <br>
        """

        if (eventBody != null) {
            print """
                ${eventBody}
                <br>
            """
        }

        if (replyable)
            print """
                <div>このメールに返信してコメントを投稿するか、<a href='${eventUrl}'>このリンク</a>をクリックして詳細をご確認ください</div>
            """
        else
            print """
                <div><a href='${eventUrl}'>このリンク</a>をクリックして詳細をご確認ください</div>
            """
	} else {
        print "${eventSummary}\n\n"

        if (eventBody != null)
            print "${eventBody}\n\n"

        if (replyable)
            print "このメールに返信してコメントを投稿するか、${eventUrl}にアクセスして詳細をご確認ください"
        else
            print "${eventUrl}にアクセスして詳細をご確認ください"
	}
%>

<%
	if (unsubscribable != null) {
	    if (htmlVersion) {
            print """
                <div style='border-top:1px solid #EEE; margin-top:1em; padding-top:1em; color:#666; font-size:0.9em;'>このトピックに
                参加しているため、この通知を受信しています。
            """
            if (unsubscribable.getEmailAddress() != null)
                print """
                    <a href='mailto:${unsubscribable.emailAddress}?subject=Unsubscribe&body=I would like not to get any notifications from this topic'>このアドレス</a>にメールを送信して購読を解除できます
                    </div>
                """
            else
                print """
                    この通知の受信を停止するには、上記の詳細リンクにアクセスし、フォローを解除してください。
                    </div>
                """
		} else {
            print "\n\n---------------------------------------------\n"
            print "このトピックに参加しているため、この通知を受信しています。"
            if (unsubscribable.getEmailAddress() != null)
                print "${unsubscribable.emailAddress}にメールを送信することで購読を解除できます"
            else
                print "この通知の受信を停止するには、上記の詳細リンクにアクセスし、フォローを解除してください。"
		}
	}
%>
