package net.devgrip.server.web.page.simple.security;

import net.devgrip.server.web.WebSession;
import net.devgrip.server.web.page.base.BasePage;
import org.apache.wicket.RestartResponseException;
import org.apache.wicket.request.mapper.parameter.PageParameters;

public class LogoutPage extends BasePage {

	public LogoutPage(PageParameters params) {
		super(params);
		WebSession.get().logout();
		getSession().success(getString("LogoutPage.logoutSuccess"));
		throw new RestartResponseException(getApplication().getHomePage());
	}
	
}
