package net.devgrip.server.plugin.report.ruff;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import net.devgrip.commons.codeassist.InputSuggestion;
import net.devgrip.commons.utils.ExceptionUtils;
import net.devgrip.commons.utils.FileUtils;
import net.devgrip.commons.utils.TaskLogger;
import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Interpolative;
import net.devgrip.server.annotation.Patterns;
import net.devgrip.server.buildspec.BuildSpec;
import net.devgrip.server.buildspec.step.StepGroup;
import net.devgrip.server.codequality.CodeProblem;
import net.devgrip.server.model.Build;
import net.devgrip.server.plugin.report.problem.PublishProblemReportStep;

import javax.validation.constraints.NotEmpty;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Editable(order=10000, group=StepGroup.PUBLISH_I18N_KEY, name="PublishRuffReportStep.name")
public class PublishRuffReportStep extends PublishProblemReportStep {
	
	private static final long serialVersionUID = 1L;
	
	public PublishRuffReportStep() {
		setFailThreshold(CodeProblem.Severity.MEDIUM);
	}
	
	@Editable(order=100, name = "File_Patterns", description="PublishRuffReportStep.filePatterns.desc")
	@Interpolative(variableSuggester="suggestVariables")
	@Patterns(path=true)
	@NotEmpty
	@Override
	public String getFilePatterns() {
		return super.getFilePatterns();
	}

	@Override
	public void setFilePatterns(String filePatterns) {
		super.setFilePatterns(filePatterns);
	}
	
	@SuppressWarnings("unused")
	private static List<InputSuggestion> suggestVariables(String matchWith) {
		return BuildSpec.suggestVariables(matchWith, true, true, false);
	}
	
	@Override
	protected List<CodeProblem> process(Build build, File inputDir, File reportDir, TaskLogger logger) {
		ObjectMapper mapper = AppServer.getInstance(ObjectMapper.class);

		List<CodeProblem> problems = new ArrayList<>();
		int baseLen = inputDir.getAbsolutePath().length()+1;
		final String reportName = "ruff";
		for (File file: FileUtils.listFiles(inputDir, Lists.newArrayList("**"), Lists.newArrayList())) {
			String relativePath = file.getAbsolutePath().substring(baseLen);
			logger.log(getProcessingLog(reportName, relativePath));
			try {
				problems.addAll(RuffReportParser.parse(build, mapper.readTree(file), logger));
			} catch (Exception e) {
				throw ExceptionUtils.unchecked(e);
			}
		}
		return problems;
	}

}
