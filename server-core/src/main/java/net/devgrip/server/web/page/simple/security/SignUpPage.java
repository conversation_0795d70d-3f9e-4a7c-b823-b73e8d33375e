package net.devgrip.server.web.page.simple.security;

import static net.devgrip.server.model.User.PROP_NOTIFY_OWN_EVENTS;
import static net.devgrip.server.model.User.PROP_SERVICE_ACCOUNT;
import static net.devgrip.server.web.page.simple.security.SignUpBean.PROP_EMAIL_ADDRESS;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.credential.PasswordService;
import org.apache.shiro.authz.UnauthenticatedException;
import org.apache.wicket.RestartResponseException;
import org.apache.wicket.Session;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.SubmitLink;
import org.apache.wicket.markup.html.link.Link;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import com.google.common.collect.Sets;

import net.devgrip.commons.utils.match.StringMatcher;
import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.EmailAddressManager;
import net.devgrip.server.entitymanager.MembershipManager;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.entitymanager.UserManager;
import net.devgrip.server.model.EmailAddress;
import net.devgrip.server.model.Group;
import net.devgrip.server.model.Membership;
import net.devgrip.server.model.User;
import net.devgrip.server.model.support.administration.SecuritySetting;
import net.devgrip.server.persistence.TransactionManager;
import net.devgrip.server.security.SecurityUtils;
import net.devgrip.server.util.Path;
import net.devgrip.server.util.PathNode;
import net.devgrip.server.util.patternset.PatternSet;
import net.devgrip.server.web.editable.BeanContext;
import net.devgrip.server.web.editable.BeanEditor;
import net.devgrip.server.web.page.HomePage;
import net.devgrip.server.web.page.simple.SimplePage;

public class SignUpPage extends SimplePage {
	
	public SignUpPage(PageParameters params) {
		super(params);
		
		if (!getSecuritySetting().isEnableSelfRegister())
			throw new UnauthenticatedException("User sign-up is disabled");
		if (getLoginUser() != null)
			throw new IllegalStateException("Can not sign up a user while signed in");
	}
	
	private SecuritySetting getSecuritySetting() {
		return AppServer.getInstance(SettingManager.class).getSecuritySetting();
	}
	
	@Override
	protected void onInitialize() {
		super.onInitialize();

		SignUpBean bean = new SignUpBean();
		BeanEditor editor = BeanContext.edit("editor", bean, Sets.newHashSet(PROP_SERVICE_ACCOUNT, PROP_NOTIFY_OWN_EVENTS), true);
		
		Form<?> form = new Form<Void>("form") {

			@Override
			protected void onSubmit() {
				super.onSubmit();
				
				User userWithSameName = getUserManager().findByName(bean.getName());
				if (userWithSameName != null) {
					editor.error(new Path(new PathNode.Named(User.PROP_NAME)),
							"simple.userSignup.nameAlreadyUsed");
				} 

				var invalidEmailAddress = false;
				if (getSecuritySetting().getAllowedSelfRegisterEmailDomain() != null) {
					var emailDomain = StringUtils.substringAfter(bean.getEmailAddress(), "@").toLowerCase();
					var patternSet = PatternSet.parse(getSecuritySetting().getAllowedSelfRegisterEmailDomain().toLowerCase());
					if (!patternSet.matches(new StringMatcher(), emailDomain)) {
						editor.error(new Path(new PathNode.Named(PROP_EMAIL_ADDRESS)),
								"simple.userSignup.emailNotAllowed");
						invalidEmailAddress = true;
					}
				}
				if (!invalidEmailAddress && getEmailAddressManager().findByValue(bean.getEmailAddress()) != null) {
					editor.error(new Path(new PathNode.Named(PROP_EMAIL_ADDRESS)),
							"simple.userSignup.emailAlreadyUsed");
				} 
				if (editor.isValid()) {
					User user = new User();
					user.setName(bean.getName());
					user.setFullName(bean.getFullName());
					user.setPassword(getPasswordService().encryptPassword(bean.getPassword()));
					
					EmailAddress emailAddress = new EmailAddress();
					emailAddress.setValue(bean.getEmailAddress());
					emailAddress.setOwner(user);

					var defaultLoginGroup = getSettingManager().getSecuritySetting().getDefaultGroup();
					
					getTransactionManager().run(() -> {
						getUserManager().create(user);
						getEmailAddressManager().create(emailAddress);
						if (defaultLoginGroup != null) 
							createMembership(user, defaultLoginGroup);
					});
					
					Session.get().success(getString("simple.userSignup.success"));
					SecurityUtils.getSubject().runAs(user.getPrincipals());
					setResponsePage(HomePage.class);
				}
			}
			
		};
		form.add(editor);
		
		form.add(new SubmitLink("save"));
		form.add(new Link<Void>("cancel") {

			@Override
			public void onClick() {
				throw new RestartResponseException(HomePage.class);
			}
			
		});
		add(form);
	}

	private PasswordService getPasswordService() {
		return AppServer.getInstance(PasswordService.class);
	}
	
	private UserManager getUserManager() {
		return AppServer.getInstance(UserManager.class);
	}
	
	private EmailAddressManager getEmailAddressManager() {
		return AppServer.getInstance(EmailAddressManager.class);
	}

	private SettingManager getSettingManager() {
		return AppServer.getInstance(SettingManager.class);
	}

	private TransactionManager getTransactionManager() {
		return AppServer.getInstance(TransactionManager.class);
	}

	private MembershipManager getMembershipManager() {
		return AppServer.getInstance(MembershipManager.class);
	}
	
	private void createMembership(User user, Group group) {
		var membership = new Membership();
		membership.setUser(user);
		membership.setGroup(group);
		user.getMemberships().add(membership);
		getMembershipManager().create(membership);
	}

	@Override
	protected String getTitle() {
		return getString("simple.userSignup.signUp");
	}

	@Override
	protected String getSubTitle() {
		return getString("simple.userSignup.enterYourDetails");
	}

}
