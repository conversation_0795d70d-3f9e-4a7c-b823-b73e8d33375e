package net.devgrip.server.x.trivy;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.NoSpace;

import java.io.Serializable;

@Editable
public class LicenseSetting implements Serializable {
   private static final long serialVersionUID = 1L;
   private String ignoredLicenses;

   @Editable(
      name = "LicenseSetting.ignoreLic.name",
      placeholder = "LicenseSetting.ignoreLic.placeholder",
      description = "LicenseSetting.ignoreLic.desc"
   )
   @NoSpace
   public String getIgnoredLicenses() {
      return this.ignoredLicenses;
   }

   public void setIgnoredLicenses(String ignoredLicenses) {
      this.ignoredLicenses = ignoredLicenses;
   }

   public String getLicenseOptions() {
      return this.getIgnoredLicenses() != null ? "--ignored-licenses " + this.getIgnoredLicenses() : "";
   }
}
