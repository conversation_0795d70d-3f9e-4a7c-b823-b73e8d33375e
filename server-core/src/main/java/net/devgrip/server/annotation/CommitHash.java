package net.devgrip.server.annotation;

import net.devgrip.server.validation.validator.CommitHashValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy=CommitHashValidator.class) 
public @interface CommitHash {
	
    String message() default "{NotAValidCommitHash}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
