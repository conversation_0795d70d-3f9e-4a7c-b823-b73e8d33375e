<wicket:panel>
	<div class="new-issue">
		<div class="form-group">
			<label class="control-label"><wicket:message key="IssueEditableTitlePanel.title"></wicket:message> <span class="text-danger">*</span></label>
			<input wicket:id="title" type="text" class="form-control" placeholder="Input title here" wicket:message="placeholder:NewPullRequestPage.inputTitle.placeholder">
			<div wicket:id="titleFeedback"></div>
		</div>
		<div wicket:id="similarIssues" class="similar-issues alert alert-light">
			<h6><wicket:message key="IssueEditableTitlePanel.similarIssues"></wicket:message></h6>
			<table class="table mb-0">
				<tr wicket:id="similarIssues">
					<td class="state"><span wicket:id="state"></span></td>
					<td wicket:id="numberAndTitle"></td> 
				</tr>
			</table>
		</div>
		<div class="form-group description">
			<label class="control-label"><wicket:message key="Description"></wicket:message></label>
			<div wicket:id="description"></div>
			<div wicket:id="descriptionFeedback"></div>
		</div>		
		<div class="form-group confidential">
			<label class="control-label"><wicket:message key="BatchEditPanel.confidential"></wicket:message></label>
			<span class="switch switch-sm switch-primary">
				<label>
					<input wicket:id="confidential" type="checkbox">
				</label>
			</span>
		</div>
		<wicket:enclosure child="iterations">
		<div class="form-group">
			<label class="control-label"><wicket:message key="Iterations"></wicket:message></label>
			<input wicket:id="iterations" type="hidden" class="form-control">
		</div>		
		</wicket:enclosure>
		<div wicket:id="fields"></div>
		<div wicket:id="estimatedTime"></div>
	</div>
</wicket:panel>
