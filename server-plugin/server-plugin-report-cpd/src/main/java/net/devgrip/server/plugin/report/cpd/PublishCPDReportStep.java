package net.devgrip.server.plugin.report.cpd;

import com.google.common.base.Splitter;
import net.devgrip.commons.codeassist.InputSuggestion;
import net.devgrip.commons.utils.FileUtils;
import net.devgrip.commons.utils.PlanarRange;
import net.devgrip.commons.utils.TaskLogger;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Interpolative;
import net.devgrip.server.annotation.Patterns;
import net.devgrip.server.buildspec.BuildSpec;
import net.devgrip.server.buildspec.step.StepGroup;
import net.devgrip.server.codequality.CodeProblem;
import net.devgrip.server.codequality.CodeProblem.Severity;
import net.devgrip.server.codequality.BlobTarget;
import net.devgrip.server.git.BlobIdent;
import net.devgrip.server.model.Build;
import net.devgrip.server.plugin.report.problem.PublishProblemReportStep;
import net.devgrip.server.util.XmlUtils;
import net.devgrip.server.web.page.project.blob.ProjectBlobPage;
import net.devgrip.server.web.page.project.blob.render.BlobRenderer;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.request.mapper.parameter.PageParametersEncoder;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.unbescape.html.HtmlEscape;

import javax.validation.constraints.NotEmpty;
import java.io.File;
import java.io.IOException;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Editable(order=10000, group=StepGroup.PUBLISH_I18N_KEY, name="PublishCPDReportStep.name")
public class PublishCPDReportStep extends PublishProblemReportStep {

	private static final long serialVersionUID = 1L;
	
	@Editable(order=100, name = "File_Patterns", description="PublishCPDReportStep.filePatterns.desc")
	@Interpolative(variableSuggester="suggestVariables")
	@Patterns(path=true)
	@NotEmpty
	@Override
	public String getFilePatterns() {
		return super.getFilePatterns();
	}

	@Override
	public void setFilePatterns(String filePatterns) {
		super.setFilePatterns(filePatterns);
	}
	
	@SuppressWarnings("unused")
	private static List<InputSuggestion> suggestVariables(String matchWith) {
		return BuildSpec.suggestVariables(matchWith, true, true, false);
	}
	
	@Override
	protected List<CodeProblem> process(Build build, File inputDir, File reportDir, TaskLogger logger) {
		int baseLen = inputDir.getAbsolutePath().length() + 1;
		SAXReader reader = new SAXReader();
		XmlUtils.disallowDocTypeDecl(reader);

		List<CodeProblem> problems = new ArrayList<>();
		final String reportName = "CPD";
		for (File file: getPatternSet().listFiles(inputDir)) {
			String relativePath = file.getAbsolutePath().substring(baseLen);
			logger.log(getProcessingLog(reportName, relativePath));
			try {
				String xml = FileUtils.readFileToString(file, StandardCharsets.UTF_8);
				Document doc = reader.read(new StringReader(XmlUtils.stripDoctype(xml)));

				for (Element duplicationElement: doc.getRootElement().elements("duplication")) {
					List<CodeDuplication> duplications = new ArrayList<>();
					for (Element fileElement: duplicationElement.elements("file")) {
						var filePath = fileElement.attributeValue("path");
						String blobPath = build.getBlobPath(filePath);
						if (blobPath != null) {
							int beginLine = Integer.parseInt(fileElement.attributeValue("line"));
							int endLine = Integer.parseInt(fileElement.attributeValue("endline"));
							int beginColumn = Integer.parseInt(fileElement.attributeValue("column"));
							int endColumn = Integer.parseInt(fileElement.attributeValue("endcolumn"));
							PlanarRange location = new PlanarRange(beginLine-1, beginColumn-1, endLine-1, endColumn);
							CodeDuplication duplication = new CodeDuplication();
							duplication.blobPath = blobPath;
							duplication.location = location;
							duplications.add(duplication);
						} else {
							logger.warning(getUnableFindBlobLog(filePath));
						}
					}
					if (duplications.size() >= 2) {
						for (int i=0; i<duplications.size(); i++) {
							CodeDuplication duplication = duplications.get(i);
							CodeDuplication duplicateWith;
							if (i == duplications.size()-1)
								duplicateWith = duplications.get(0);
							else
								duplicateWith = duplications.get(i+1);
							
							PageParameters params = new PageParameters();
							ProjectBlobPage.State state = new ProjectBlobPage.State();
							state.blobIdent = new BlobIdent();
							state.problemReport = getReportName();
							state.position = BlobRenderer.getSourcePosition(duplicateWith.location); 
							
							params.set(0, build.getCommitHash());
							List<String> pathSegments = Splitter.on("/").splitToList(duplicateWith.blobPath);
							for (int j=0; j<pathSegments.size(); j++) 
								params.set(j+1, pathSegments.get(j));
							
							ProjectBlobPage.fillParams(params, state);
							
							PageParametersEncoder paramsEncoder = new PageParametersEncoder();
							String url  = "/" + build.getProject().getPath() + "/~files/" + paramsEncoder.encodePageParameters(params);
							String defaultDup = "Duplicated with '%s' at <a href='%s'>line %s - %s</a>";
							String dupMsg = String.format(getI18nManager().getOrDefault("PublishCPDReportStep.dupMsg", defaultDup), HtmlEscape.escapeHtml5(duplicateWith.blobPath), url, duplicateWith.location.getFromRow()+1, duplicateWith.location.getToRow()+1);
							CodeProblem problem = new CodeProblem(
									Severity.LOW, 
									new BlobTarget(duplication.blobPath, duplication.location),
									dupMsg);
							problems.add(problem);
						}
					}
				}
			} catch (DocumentException e) {
				logger.warning(getIgnoredLog(reportName, relativePath));
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
		}
		return problems;
	}

	private static class CodeDuplication {
		
		String blobPath;
		
		PlanarRange location;
		
	}
}
