package net.devgrip.server.web.component.project.choice;

import net.devgrip.server.model.Project;
import net.devgrip.server.web.component.select2.Select2MultiChoice;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.ResourceModel;

import java.util.Collection;
import java.util.List;

public class ProjectMultiChoice extends Select2MultiChoice<Project> {

	private static final long serialVersionUID = 1L;

	public ProjectMultiChoice(String id, IModel<Collection<Project>> model, IModel<List<Project>> choicesModel) {
		super(id, model, new ProjectChoiceProvider(choicesModel));
	}

	@Override
	protected void onInitialize() {
		super.onInitialize();
		if (isRequired())
			getSettings().setPlaceholder(new ResourceModel("ProjectChoice.choosePro.placeholder", "Choose a project...").getObject());
		else
			getSettings().setPlaceholder(new ResourceModel("Not_Specified", "Not specified").getObject());
		getSettings().setFormatResult("onedev.server.projectChoiceFormatter.formatResult");
		getSettings().setFormatSelection("onedev.server.projectChoiceFormatter.formatSelection");
		getSettings().setEscapeMarkup("onedev.server.projectChoiceFormatter.escapeMarkup");
		setConvertEmptyInputStringToNull(true);
	}

	@Override
	public void renderHead(IHeaderResponse response) {
		super.renderHead(response);
		
		response.render(JavaScriptHeaderItem.forReference(new ProjectChoiceResourceReference()));
	}

}
