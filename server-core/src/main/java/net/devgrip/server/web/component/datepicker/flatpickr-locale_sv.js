(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.sv = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Swedish = {
      firstDayOfWeek: 1,
      weekAbbreviation: "v",
      weekdays: {
          shorthand: ["<PERSON>ön", "<PERSON><PERSON>n", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Fre", "<PERSON><PERSON><PERSON>"],
          longhand: [
              "Söndag",
              "Måndag",
              "Tisdag",
              "Onsdag",
              "Torsdag",
              "Fredag",
              "Lördag",
          ],
      },
      months: {
          shorthand: [
              "<PERSON>",
              "Feb",
              "<PERSON>",
              "<PERSON>",
              "<PERSON>",
              "<PERSON>",
              "<PERSON>",
              "Aug",
              "<PERSON>",
              "<PERSON><PERSON>",
              "Nov",
              "Dec",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON>",
              "April",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "Augusti",
              "September",
              "Oktober",
              "November",
              "December",
          ],
      },
      time_24hr: true,
      ordinal: function () {
          return ".";
      },
  };
  fp.l10ns.sv = Swedish;
  var sv = fp.l10ns;

  exports.Swedish = Swedish;
  exports.default = sv;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
