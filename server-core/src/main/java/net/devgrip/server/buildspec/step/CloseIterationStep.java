package net.devgrip.server.buildspec.step;

import net.devgrip.commons.codeassist.InputSuggestion;
import net.devgrip.commons.utils.TaskLogger;
import net.devgrip.k8shelper.ServerStepResult;
import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.ChoiceProvider;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Interpolative;
import net.devgrip.server.buildspec.BuildSpec;
import net.devgrip.server.entitymanager.BuildManager;
import net.devgrip.server.entitymanager.IterationManager;
import net.devgrip.server.i18n.I18nManager;
import net.devgrip.server.model.Iteration;
import net.devgrip.server.model.Project;
import net.devgrip.server.persistence.TransactionManager;

import javax.validation.constraints.NotEmpty;
import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

@Editable(name="CloseIterationStep.name", order=400)
public class CloseIterationStep extends ServerSideStep {

	private static final long serialVersionUID = 1L;
	
	private String iterationName;
	
	private String accessTokenSecret;
	
	@Editable(order=1000, name = "CloseIterationStep.iteration.name", description="CloseIterationStep.iteration.desc")
	@Interpolative(variableSuggester="suggestVariables")
	@NotEmpty
	public String getIterationName() {
		return iterationName;
	}

	public void setIterationName(String iterationName) {
		this.iterationName = iterationName;
	}
	
	@SuppressWarnings("unused")
	private static List<InputSuggestion> suggestVariables(String matchWith) {
		return BuildSpec.suggestVariables(matchWith, true, true, false);
	}

	@Editable(order=1060, name = "AccessTokenSecret", description= "CloseIterationStep.ats.desc")
	@ChoiceProvider("getAccessTokenSecretChoices")
	public String getAccessTokenSecret() {
		return accessTokenSecret;
	}

	public void setAccessTokenSecret(String accessTokenSecret) {
		this.accessTokenSecret = accessTokenSecret;
	}

	@SuppressWarnings("unused")
	private static List<String> getAccessTokenSecretChoices() {
		return Project.get().getHierarchyJobSecrets()
				.stream().map(it->it.getName()).collect(Collectors.toList());
	}

	private I18nManager getI18nManager() {
		return AppServer.getInstance(I18nManager.class);
	}
	
	@Override
	public ServerStepResult run(Long buildId, File inputDir, TaskLogger logger) {
		return AppServer.getInstance(TransactionManager.class).call(() -> {
			var build = AppServer.getInstance(BuildManager.class).load(buildId);
			Project project = build.getProject();
			String iterationName = getIterationName();
			IterationManager iterationManager = AppServer.getInstance(IterationManager.class);
			Iteration iteration = iterationManager.findInHierarchy(project, iterationName);
			if (iteration != null) {
				if (build.canCloseIteration(getAccessTokenSecret())) {
					iteration.setClosed(true);
					iterationManager.createOrUpdate(iteration);
				} else {
					String defaultError = "This build is not authorized to close iteration '%s'";
					String msg = String.format(getI18nManager().getOrDefault("CloseIterationStep.error.noAuth", defaultError), iterationName);
					logger.error(msg);
					return new ServerStepResult(false);
				}
			} else {
				String defaultError = "Unable to find iteration '%s' to close. Ignored.";
				String msg = String.format(getI18nManager().getOrDefault("CloseIterationStep.error.unableFind", defaultError), iterationName);
				logger.warning(msg);
			}
			return new ServerStepResult(true);
		});
	}

}
