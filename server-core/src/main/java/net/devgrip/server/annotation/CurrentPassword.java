package net.devgrip.server.annotation;

import net.devgrip.server.validation.validator.CurrentPasswordValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy=CurrentPasswordValidator.class) 
public @interface CurrentPassword {

	String message() default "{CurrentPasswordDoesNotMatch}";
	
	Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
