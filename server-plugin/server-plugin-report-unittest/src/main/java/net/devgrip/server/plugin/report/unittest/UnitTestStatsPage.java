package net.devgrip.server.plugin.report.unittest;

import org.apache.wicket.Component;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.model.ResourceModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import net.devgrip.server.model.UnitTestMetric;
import net.devgrip.server.web.page.project.stats.buildmetric.BuildMetricStatsPage;

public class UnitTestStatsPage extends BuildMetricStatsPage<UnitTestMetric> {

	public UnitTestStatsPage(PageParameters params) {
		super(params);
	}

	@Override
	protected Component newProjectTitle(String componentId) {
		return new Label(componentId, new ResourceModel("UnitTestStatsPage.title"));
	}

}
