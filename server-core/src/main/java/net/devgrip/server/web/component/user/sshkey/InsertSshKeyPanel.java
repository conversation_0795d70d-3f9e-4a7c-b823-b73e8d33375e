package net.devgrip.server.web.component.user.sshkey;

import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.SshKeyManager;
import net.devgrip.server.model.SshKey;
import net.devgrip.server.model.User;
import net.devgrip.server.util.Path;
import net.devgrip.server.util.PathNode;
import net.devgrip.server.web.editable.BeanContext;
import net.devgrip.server.web.editable.BeanEditor;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.ResourceModel;

import java.util.Date;

public abstract class InsertSshKeyPanel extends Panel {

    public InsertSshKeyPanel(String id) {
        super(id);
    }

    @Override
    protected void onInitialize() {
        super.onInitialize();
        
        add(new AjaxLink<Void>("close") {

            @Override
            public void onClick(AjaxRequestTarget target) {
            	onCancel(target);
            }
            
        });
        
        Form<?> form = new Form<Void>("form");
        
        BeanEditor editor = BeanContext.edit("editor", new SshKey());
        form.add(editor);
        
        form.add(new AjaxButton("add") {
        	
            @Override
            protected void onSubmit(AjaxRequestTarget target, Form<?> myform) {
                super.onSubmit(target, myform);
                
                SshKeyManager sshKeyManager = AppServer.getInstance(SshKeyManager.class);
                SshKey sshKey = (SshKey) editor.getModelObject();
                sshKey.fingerprint();
                
                if (sshKeyManager.findByFingerprint(sshKey.getFingerprint()) != null) {
					editor.error(new Path(new PathNode.Named("content")), new ResourceModel("InsertSshKeyPanel.add.fail", "This key is already in use").getObject());
					target.add(form);
                } else {
                    sshKey.setOwner(getUser());
                    sshKey.setCreatedAt(new Date());
                    sshKeyManager.create(sshKey);
                    onSave(target);
                }
            }
            
            @Override
            protected void onError(AjaxRequestTarget target, Form<?> form) {
                super.onError(target, form);
                target.add(form);
            }
            
        });
        
        form.add(new AjaxLink<Void>("cancel") {
            
        	@Override
            public void onClick(AjaxRequestTarget target) {
        		onCancel(target);
            }
            
        });
        
        add(form.setOutputMarkupId(true));
    }
    
    protected abstract User getUser();
    
    protected abstract void onSave(AjaxRequestTarget target);
    
    protected abstract void onCancel(AjaxRequestTarget target);

	@Override
	public void renderHead(IHeaderResponse response) {
		super.renderHead(response);
		response.render(CssHeaderItem.forReference(new SshKeyCssResourceReference()));
	}
    
}
