package net.devgrip.server.web.component.job;

import java.util.Collection;
import java.util.List;

import org.apache.wicket.model.IModel;

import net.devgrip.server.web.component.select2.Select2MultiChoice;
import net.devgrip.server.web.component.stringchoice.StringChoiceProvider;

public class Job<PERSON>ultiC<PERSON><PERSON> extends Select2MultiChoice<String> {

	private static final long serialVersionUID = 1L;

	public JobMultiChoice(String id, IModel<Collection<String>> model, IModel<List<String>> choicesModel, boolean tagsMode) {
		super(id, model, new StringChoiceProvider(choicesModel, tagsMode));
	}

	@Override
	protected void onInitialize() {
		super.onInitialize();
		if (isRequired())
			getSettings().setPlaceholder(getString("JobMultiChoice.chooseJobs"));
		else
			getSettings().setPlaceholder(getString("Not_Specified"));
		getSettings().setFormatResult("onedev.server.choiceFormatter.formatResult");
		getSettings().setFormatSelection("onedev.server.choiceFormatter.formatSelection");
		getSettings().setEscapeMarkup("onedev.server.choiceFormatter.escapeMarkup");
		setConvertEmptyInputStringToNull(true);
	}

}
