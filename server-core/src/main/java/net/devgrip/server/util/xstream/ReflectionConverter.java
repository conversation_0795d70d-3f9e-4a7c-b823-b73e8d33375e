package net.devgrip.server.util.xstream;

import java.lang.reflect.Field;

import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

import com.thoughtworks.xstream.converters.MarshallingContext;
import com.thoughtworks.xstream.converters.UnmarshallingContext;
import com.thoughtworks.xstream.converters.reflection.ReflectionProvider;
import com.thoughtworks.xstream.mapper.Mapper;

import net.devgrip.commons.loader.AppLoader;
import net.devgrip.server.model.AbstractEntity;
import net.devgrip.server.persistence.dao.Dao;

public class ReflectionConverter extends com.thoughtworks.xstream.converters.reflection.ReflectionConverter {

	public ReflectionConverter(Mapper mapper, ReflectionProvider reflectionProvider) {
		super(mapper, reflectionProvider);
	}
	
    @SuppressWarnings("rawtypes")
	public ReflectionConverter(Mapper mapper, ReflectionProvider reflectionProvider, Class type) {
    	super(mapper, reflectionProvider, type);
    }
    
	@Override
	protected void marshallField(MarshallingContext context, Object newObj, Field field) {
		if (field.getAnnotation(ManyToOne.class) != null || field.getAnnotation(JoinColumn.class) != null) 
			super.marshallField(context, ((AbstractEntity) newObj).getId(), field);
		else 
			super.marshallField(context, newObj, field);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	protected Object unmarshallField(UnmarshallingContext context, Object result, Class type, Field field) {
		if (field.getAnnotation(ManyToOne.class) != null || field.getAnnotation(JoinColumn.class) != null) {
			Long entityId = (Long) context.convertAnother(context.currentObject(), Long.class);
			return AppLoader.getInstance(Dao.class).load((Class<? extends AbstractEntity>) field.getType(), entityId);
		} else {
			return super.unmarshallField(context, result, type, field);
		}
	}
	
}
