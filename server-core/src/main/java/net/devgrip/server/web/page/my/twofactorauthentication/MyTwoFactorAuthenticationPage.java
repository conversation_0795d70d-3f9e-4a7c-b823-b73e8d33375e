package net.devgrip.server.web.page.my.twofactorauthentication;

import net.devgrip.commons.utils.ExplicitException;
import net.devgrip.server.model.User;
import net.devgrip.server.web.component.user.twofactorauthentication.TwoFactorAuthenticationStatusPanel;
import net.devgrip.server.web.page.my.MyPage;
import org.apache.wicket.Component;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.request.mapper.parameter.PageParameters;

public class MyTwoFactorAuthenticationPage extends MyPage {

	public MyTwoFactorAuthenticationPage(PageParameters params) {
		super(params);
		if (getUser().isServiceAccount() || getUser().isDisabled())
			throw new IllegalStateException();
		else if (!getUser().isEnforce2FA())
			throw new ExplicitException("Two-factor authentication not enabled");		
	}
	
	@Override
	protected void onInitialize() {
		super.onInitialize();

		add(new TwoFactorAuthenticationStatusPanel("content") {
			@Override
			protected User getUser() {
				return MyTwoFactorAuthenticationPage.this.getUser();
			}

		});
	}

	@Override
	protected Component newTopbarTitle(String componentId) {
		return new Label(componentId, getString("myTwoFactorAuthentication.title"));
	}

}
