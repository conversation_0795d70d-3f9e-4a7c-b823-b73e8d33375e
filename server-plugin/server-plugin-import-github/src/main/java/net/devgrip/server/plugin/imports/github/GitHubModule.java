package net.devgrip.server.plugin.imports.github;

import com.google.common.collect.Lists;
import net.devgrip.commons.loader.AbstractPluginModule;
import net.devgrip.server.imports.IssueImporter;
import net.devgrip.server.imports.IssueImporterContribution;
import net.devgrip.server.imports.ProjectImporter;
import net.devgrip.server.imports.ProjectImporterContribution;

import java.util.Collection;

/**
 * NOTE: Do not forget to rename moduleClass property defined in the pom if you've renamed this class.
 *
 */
public class GitHubModule extends AbstractPluginModule {

	static final String NAME = "GitHub";

	@Override
	protected void configure() {
		super.configure();
		
		contribute(ProjectImporterContribution.class, new ProjectImporterContribution() {

			@Override
			public Collection<ProjectImporter> getImporters() {
				return Lists.newArrayList(new GitHubProjectImporter());
			}

			@Override
			public int getOrder() {
				return 150;
			}
			
		});
		
		contribute(IssueImporterContribution.class, new IssueImporterContribution() {

			@Override
			public Collection<IssueImporter> getImporters() {
				return Lists.newArrayList(new GitHubIssueImporter());
			}

			@Override
			public int getOrder() {
				return 150;
			}
			
		});
	}
	
}
