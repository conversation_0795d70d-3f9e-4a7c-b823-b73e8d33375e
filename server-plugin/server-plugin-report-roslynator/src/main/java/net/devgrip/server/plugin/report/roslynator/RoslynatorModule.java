package net.devgrip.server.plugin.report.roslynator;

import com.google.common.collect.Sets;
import net.devgrip.commons.loader.AbstractPluginModule;
import net.devgrip.commons.loader.ImplementationProvider;
import net.devgrip.server.buildspec.step.PublishReportStep;

import java.util.Collection;

/**
 * NOTE: Do not forget to rename moduleClass property defined in the pom if you've renamed this class.
 *
 */
public class RoslynatorModule extends AbstractPluginModule {

	@Override
	protected void configure() {
		super.configure();
		
		contribute(ImplementationProvider.class, new ImplementationProvider() {

			@Override
			public Class<?> getAbstractClass() {
				return PublishReportStep.class;
			}
			
			@Override
			public Collection<Class<?>> getImplementations() {
				return Sets.newHashSet(PublishRoslynatorReportStep.class);
			}
			
		});
	}

}
