package net.devgrip.server.web.resource;

import static net.devgrip.commons.utils.LockUtils.read;
import static net.devgrip.server.util.IOUtils.copyRange;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityNotFoundException;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.wicket.request.flow.RedirectToUrlException;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.request.resource.AbstractResource;
import org.eclipse.jetty.io.EofException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;

import net.devgrip.commons.utils.ExplicitException;
import net.devgrip.k8shelper.KubernetesHelper;
import net.devgrip.server.AppServer;
import net.devgrip.server.cluster.ClusterManager;
import net.devgrip.server.entitymanager.ProjectManager;
import net.devgrip.server.exception.ExceptionUtils;
import net.devgrip.server.git.BlobIdent;
import net.devgrip.server.model.Project;
import net.devgrip.server.util.LongRange;
import net.devgrip.server.util.artifact.ArtifactInfo;
import net.devgrip.server.util.artifact.DirectoryInfo;
import net.devgrip.server.util.artifact.FileInfo;
import net.devgrip.server.web.mapper.ProjectMapperUtils;
import net.devgrip.server.web.util.WicketUtils;

public class SiteFileResource extends AbstractResource {

	private static final long serialVersionUID = 1L;
	
	private static final Logger logger = LoggerFactory.getLogger(SiteFileResource.class);

	@Override
	protected ResourceResponse newResourceResponse(Attributes attributes) {
		PageParameters params = attributes.getParameters();

		String projectPath = params.get(ProjectMapperUtils.PARAM_PROJECT).toString();
		Project project = getProjectManager().findByPath(projectPath);
		if (project == null)
			throw new EntityNotFoundException();
		
		Long projectId = project.getId();
		
		List<String> filePathSegments = new ArrayList<>();
		for (int i = 0; i < params.getIndexedCount(); i++) {
			String segment = params.get(i).toString();
			if (segment.contains(".."))
				throw new ExplicitException("Invalid request path");
			if (segment.length() != 0)
				filePathSegments.add(segment);
		}
		
		FileInfo fileInfo;
		String filePath = Joiner.on("/").join(filePathSegments);
		if (filePath.length() != 0) {
			ArtifactInfo artifactInfo = getProjectManager().getSiteArtifactInfo(projectId, filePath);
			if (artifactInfo instanceof DirectoryInfo) {
				if (attributes.getRequest().getUrl().getPath().endsWith("/")) {
					filePath += "/index.html";
					artifactInfo = getProjectManager().getSiteArtifactInfo(projectId, filePath);
					if (artifactInfo instanceof FileInfo)
						fileInfo = (FileInfo) artifactInfo;
					else						
						return newNotFoundResponse(filePath);
				} else {
					throw new RedirectToUrlException(attributes.getRequest().getUrl().getPath() + "/");
				}
			} else if (artifactInfo instanceof FileInfo) {
				fileInfo = (FileInfo) artifactInfo;
			} else {
				return newNotFoundResponse(filePath);
			}
		} else if (attributes.getRequest().getUrl().getPath().endsWith("/")) {
			filePath = "index.html";
			ArtifactInfo artifactInfo = getProjectManager().getSiteArtifactInfo(projectId, filePath);
			if (artifactInfo instanceof FileInfo)
				fileInfo = (FileInfo) artifactInfo;
			else
				return newNotFoundResponse(filePath);
		} else {
			throw new RedirectToUrlException(attributes.getRequest().getUrl().getPath() + "/");
		}
		
		ResourceResponse response = new ResourceResponse();
		response.setAcceptRange(ContentRangeType.BYTES);
		response.setContentType(fileInfo.getMediaType());
		response.setContentLength(fileInfo.getLength());
		
		try {
			response.setFileName(URLEncoder.encode(filePath, StandardCharsets.UTF_8.name()));
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		}

		String finalFilePath = filePath;
		response.setWriteCallback(new WriteCallback() {

			private void handle(Exception e) {
				EofException eofException = ExceptionUtils.find(e, EofException.class);
				if (eofException != null) 
					logger.trace("EOF while writing data", eofException);
				else 
					throw ExceptionUtils.unchecked(e);
			}
			
			@Override
			public void writeData(Attributes attributes) throws IOException {
				LongRange range = WicketUtils.getRequestContentRange(fileInfo.getLength());
				
				String activeServer = getProjectManager().getActiveServer(projectId, true);
				if (activeServer.equals(getClusterManager().getLocalServerAddress())) {
					read(Project.getSiteLockName(projectId), () -> {
						File file = new File(getProjectManager().getSiteDir(projectId), finalFilePath);
						try (InputStream is = new FileInputStream(file)) {
							copyRange(is, attributes.getResponse().getOutputStream(), range);
						} catch (IOException e) {
							handle(e);
						}
						return null;
					});
				} else {
					Client client = ClientBuilder.newClient();
					try {
						String serverUrl = getClusterManager().getServerUrl(activeServer);
						WebTarget target = client.target(serverUrl);
						target = target.path("~api/cluster/site")
								.queryParam("projectId", project.getId())
								.queryParam("filePath", finalFilePath);
						Invocation.Builder builder =  target.request();
						builder.header(HttpHeaders.AUTHORIZATION, 
								KubernetesHelper.BEARER + " " + getClusterManager().getCredential());
						try (Response response = builder.get()){
							KubernetesHelper.checkStatus(response);
							try (InputStream is = response.readEntity(InputStream.class)) {
								copyRange(is, attributes.getResponse().getOutputStream(), range);
							} catch (Exception e) {
								handle(e);
							}
						}
					} finally {
						client.close();
					}
				}
				
			}

		});

		return response;
	}
	
	private ResourceResponse newNotFoundResponse(String filePath) {
		ResourceResponse response = new ResourceResponse();
		response.setStatusCode(HttpServletResponse.SC_NOT_FOUND).setContentType(MediaType.TEXT_PLAIN);
		return new ResourceResponse().setWriteCallback(new WriteCallback() {
			@Override
			public void writeData(Attributes attributes) throws IOException {
				attributes.getResponse().write("Site file not found: " + filePath);
			}
			
		});			
	}

	private ProjectManager getProjectManager() {
		return AppServer.getInstance(ProjectManager.class);
	}
	
	private ClusterManager getClusterManager() {
		return AppServer.getInstance(ClusterManager.class);
	}
	
	public static PageParameters paramsOf(Project project, BlobIdent blobIdent) {
		PageParameters params = new PageParameters();
		params.set(ProjectMapperUtils.PARAM_PROJECT, project.getPath());
		
		int index = 0;
		for (String segment: Splitter.on("/").split(blobIdent.revision)) {
			params.set(index, segment);
			index++;
		}
		for (String segment: Splitter.on("/").split(blobIdent.path)) {
			params.set(index, segment);
			index++;
		}

		return params;
	}

}
