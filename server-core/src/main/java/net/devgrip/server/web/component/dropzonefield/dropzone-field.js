onedev.server.dropzone = {
	onDomReady: function(containerId, uploadUrl, deleteCallback, acceptedFiles, maxFiles, maxFilesize,
						 defaultMsg, fallbackMsg, fallbackText, fileTooBig, invalidType, cancel, cancelConfirm, remove, removeConfirm, maxFilesExceeded) {
		var input = document.querySelector("#" + containerId + ">.dropzone");
		
		var dropzone = new Dropzone(input, {
			url: uploadUrl,
			addRemoveLinks: true,
			acceptedFiles: acceptedFiles,
			maxFiles: maxFiles,
			maxFilesize: maxFilesize, 
			dictDefaultMessage: defaultMsg,
			dictFallbackMessage: fallbackMsg,
			dictFallbackText: fallbackText,
			dictFileTooBig: fileTooBig,
			dictInvalidFileType: invalidType,
			dictCancelUpload: cancel,
			dictCancelUploadConfirmation: cancelConfirm,
			dictRemoveFile: remove,
			dictRemoveFileConfirmation: removeConfirm,
			dictMaxFilesExceeded: maxFilesExceeded,
			success: function() {
				onedev.server.form.markDirty($(input).closest("form"));
			},
			removedfile: function(file) {
				deleteCallback(file.name);
				$(file.previewElement).remove();
			},
			headers: {
				"Wicket-Ajax": true,
				"Wicket-Ajax-BaseURL": Wicket.Ajax.baseUrl
			}
		});
	}
}
