package net.devgrip.server.web.page.project.pullrequests;

import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.ProjectManager;
import net.devgrip.server.entitymanager.PullRequestQueryPersonalizationManager;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.PullRequestQueryPersonalization;
import net.devgrip.server.model.support.NamedQuery;
import net.devgrip.server.model.support.QueryPersonalization;
import net.devgrip.server.model.support.administration.GlobalPullRequestSetting;
import net.devgrip.server.model.support.pullrequest.NamedPullRequestQuery;
import net.devgrip.server.model.support.pullrequest.ProjectPullRequestSetting;
import net.devgrip.server.security.SecurityUtils;
import net.devgrip.server.web.component.link.ViewStateAwarePageLink;
import net.devgrip.server.web.component.modal.ModalPanel;
import net.devgrip.server.web.component.pullrequest.list.PullRequestListPanel;
import net.devgrip.server.web.component.savedquery.NamedQueriesBean;
import net.devgrip.server.web.component.savedquery.PersonalQuerySupport;
import net.devgrip.server.web.component.savedquery.SaveQueryPanel;
import net.devgrip.server.web.component.savedquery.SavedQueriesPanel;
import net.devgrip.server.web.page.project.ProjectPage;
import net.devgrip.server.web.page.project.dashboard.ProjectDashboardPage;
import net.devgrip.server.web.util.NamedPullRequestQueriesBean;
import net.devgrip.server.web.util.QuerySaveSupport;
import net.devgrip.server.web.util.paginghistory.PagingHistorySupport;
import net.devgrip.server.web.util.paginghistory.ParamPagingHistorySupport;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.html.link.Link;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.ArrayList;

public class ProjectPullRequestsPage extends ProjectPage {

	private static final String PARAM_PAGE = "page";
	
	private static final String PARAM_QUERY = "query";
	
	private String query;			
	
	private SavedQueriesPanel<NamedPullRequestQuery> savedQueries;
	
	private PullRequestListPanel requestList;
	
	public ProjectPullRequestsPage(PageParameters params) {
		super(params, true);
		query = getPageParameters().get(PARAM_QUERY).toOptionalString();
	}

	private PullRequestQueryPersonalizationManager getPullRequestQueryPersonalizationManager() {
		return AppServer.getInstance(PullRequestQueryPersonalizationManager.class);
	}
	
	protected GlobalPullRequestSetting getPullRequestSetting() {
		return AppServer.getInstance(SettingManager.class).getPullRequestSetting();
	}
	
	@Override
	protected void onInitialize() {
		super.onInitialize();

		add(savedQueries = new SavedQueriesPanel<NamedPullRequestQuery>("savedQueries", false) {

			@Override
			protected NamedQueriesBean<NamedPullRequestQuery> newNamedQueriesBean() {
				return new NamedPullRequestQueriesBean();
			}

			@Override
			protected Link<Void> newQueryLink(String componentId, NamedPullRequestQuery namedQuery) {
				return new BookmarkablePageLink<Void>(componentId, ProjectPullRequestsPage.class, 
						ProjectPullRequestsPage.paramsOf(getProject(), namedQuery.getQuery(), 0));
			}

			@Override
			protected QueryPersonalization<NamedPullRequestQuery> getQueryPersonalization() {
				return getProject().getPullRequestQueryPersonalizationOfCurrentUser();
			}

			@Override
			protected ArrayList<NamedPullRequestQuery> getCommonQueries() {
				return (ArrayList<NamedPullRequestQuery>) getProject().getPullRequestSetting().getNamedQueries();
			}

			@Override
			protected ArrayList<NamedPullRequestQuery> getInheritedCommonQueries() {
				if (getProject().getParent() != null)
					return (ArrayList<NamedPullRequestQuery>) getProject().getParent().getNamedPullRequestQueries();
				else
					return (ArrayList<NamedPullRequestQuery>) getPullRequestSetting().getNamedQueries();
			}

			@Override
			protected void onSaveCommonQueries(ArrayList<NamedPullRequestQuery> namedQueries) {
				getProject().getPullRequestSetting().setNamedQueries(namedQueries);
				AppServer.getInstance(ProjectManager.class).update(getProject());
			}

		});
		
		add(requestList = new PullRequestListPanel("pullRequests", new IModel<String>() {

			@Override
			public void detach() {
			}

			@Override
			public String getObject() {
				return query;
			}

			@Override
			public void setObject(String object) {
				query = object;
				PageParameters params = getPageParameters();
				params.set(PARAM_QUERY, query);
				params.remove(PARAM_PAGE);
				CharSequence url = RequestCycle.get().urlFor(ProjectPullRequestsPage.class, params);
				pushState(RequestCycle.get().find(AjaxRequestTarget.class), url.toString(), query);
			}
			
		}) {

			@Override
			protected PagingHistorySupport getPagingHistorySupport() {
				return new ParamPagingHistorySupport() {

					@Override
					public PageParameters newPageParameters(int currentPage) {
						PageParameters params = paramsOf(getProject(), query, 0);
						params.add(PARAM_PAGE, currentPage+1);
						return params;
					}
					
					@Override
					public int getCurrentPage() {
						return getPageParameters().get(PARAM_PAGE).toInt(1)-1;
					}
					
				};
			}

			@Override
			protected QuerySaveSupport getQuerySaveSupport() {
				return new QuerySaveSupport() {

					@Override
					public void onSaveQuery(AjaxRequestTarget target, String query) {
						new ModalPanel(target)  {

							@Override
							protected Component newContent(String id) {
								return new SaveQueryPanel(id, new PersonalQuerySupport() {

									@Override
									public void onSave(AjaxRequestTarget target, String name) {
										PullRequestQueryPersonalization setting = getProject().getPullRequestQueryPersonalizationOfCurrentUser();
										NamedPullRequestQuery namedQuery = NamedQuery.find(setting.getQueries(), name);
										if (namedQuery == null) {
											namedQuery = new NamedPullRequestQuery(name, query);
											setting.getQueries().add(namedQuery);
										} else {
											namedQuery.setQuery(query);
										}
										getPullRequestQueryPersonalizationManager().createOrUpdate(setting);
											
										target.add(savedQueries);
										close();
									}
									
								}) {

									@Override
									protected void onSave(AjaxRequestTarget target, String name) {
										ProjectPullRequestSetting setting = getProject().getPullRequestSetting();
										if (setting.getNamedQueries() == null) 
											setting.setNamedQueries(new ArrayList<>(getPullRequestSetting().getNamedQueries()));
										NamedPullRequestQuery namedQuery = getProject().getNamedPullRequestQuery(name);
										if (namedQuery == null) {
											namedQuery = new NamedPullRequestQuery(name, query);
											setting.getNamedQueries().add(namedQuery);
										} else {
											namedQuery.setQuery(query);
										}
										AppServer.getInstance(ProjectManager.class).update(getProject());
										target.add(savedQueries);
										close();
									}

									@Override
									protected void onCancel(AjaxRequestTarget target) {
										close();
									}
									
								};
							}
							
						};
					}

					@Override
					public boolean isSavedQueriesVisible() {
						savedQueries.configure();
						return savedQueries.isVisible();
					}
					
				};
			}

			@Override
			protected Project getProject() {
				return ProjectPullRequestsPage.this.getProject();
			}
			
		});
	}
	
	@Override
	protected void onPopState(AjaxRequestTarget target, Serializable data) {
		query = (String) data;
		getPageParameters().set(PARAM_QUERY, query);
		target.add(requestList);
	}
	
	@Override
	protected boolean isPermitted() {
		return SecurityUtils.canReadCode(getProject());
	}
	
	public static PageParameters paramsOf(Project project, @Nullable String query, int page) {
		PageParameters params = paramsOf(project);
		if (query != null)
			params.add(PARAM_QUERY, query);
		if (page != 0)
			params.add(PARAM_PAGE, page);
		return params;
	}

	public static PageParameters paramsOf(Project project, int page) {
		String query = null;
		if (project.getPullRequestQueryPersonalizationOfCurrentUser() != null
				&& !project.getPullRequestQueryPersonalizationOfCurrentUser().getQueries().isEmpty()) {
			query = project.getPullRequestQueryPersonalizationOfCurrentUser().getQueries().iterator().next().getQuery();
		} else if (!project.getNamedPullRequestQueries().isEmpty()) {
			query = project.getNamedPullRequestQueries().iterator().next().getQuery();
		}
		return paramsOf(project, query, page);
	}

	@Override
	protected Component newProjectTitle(String componentId) {
		return new Label(componentId, "<span class='text-nowrap'>"+getString("Pull_Requests")+"</span>").setEscapeModelStrings(false);
	}

	@Override
	protected String getPageTitle() {
		return getString("Pull_Requests") + " - " + getProject().getPath();
	}
	
	@Override
	protected BookmarkablePageLink<Void> navToProject(String componentId, Project project) {
		if (project.isCodeManagement() && SecurityUtils.canReadCode(project)) 
			return new ViewStateAwarePageLink<Void>(componentId, ProjectPullRequestsPage.class, ProjectPullRequestsPage.paramsOf(project, 0));
		else
			return new ViewStateAwarePageLink<Void>(componentId, ProjectDashboardPage.class, ProjectDashboardPage.paramsOf(project.getId()));
	}
	
}
