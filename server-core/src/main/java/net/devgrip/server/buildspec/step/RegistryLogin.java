package net.devgrip.server.buildspec.step;

import net.devgrip.commons.codeassist.InputSuggestion;
import net.devgrip.k8shelper.RegistryLoginFacade;
import net.devgrip.server.buildspec.BuildSpec;
import net.devgrip.server.model.Build;
import net.devgrip.server.model.Project;
import net.devgrip.server.annotation.ChoiceProvider;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Interpolative;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Editable
public class RegistryLogin implements Serializable {
	
	private static final long serialVersionUID = 1L;

	private String registryUrl;
	
	private String userName;
	
	private String passwordSecret;

	@Editable(order=100, name="RegistryLogin.ru.name", placeholder="Docker hub", displayPlaceholderAsValue =true, description="RegistryLogin.ru.desc")
	@Interpolative(variableSuggester = "suggestVariables")
	public String getRegistryUrl() {
		return registryUrl;
	}

	public void setRegistryUrl(String registryUrl) {
		this.registryUrl = registryUrl;
	}

	@Editable(order=200, name = "RegistryLogin.un.name", description = "StepRegistryLogin.un.desc")
	@Interpolative(variableSuggester = "suggestVariables")
	@NotEmpty
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}
	
	@Editable(order=300, name = "RegistryLogin.pwdSecret.name", description = "RegistryLogin.pwdSecret.desc")
	@ChoiceProvider("getPasswordSecretChoices")
	@NotEmpty
	public String getPasswordSecret() {
		return passwordSecret;
	}

	public void setPasswordSecret(String passwordSecret) {
		this.passwordSecret = passwordSecret;
	}

	protected static List<String> getPasswordSecretChoices() {
		return Project.get().getHierarchyJobSecrets()
				.stream().map(it->it.getName()).collect(Collectors.toList());
	}

	@SuppressWarnings("unused")
	private static List<InputSuggestion> suggestVariables(String matchWith) {
		return BuildSpec.suggestVariables(matchWith, false, false, false);
	}
	
	public RegistryLoginFacade getFacade(Build build) {
		var password = build.getJobAuthorizationContext().getSecretValue(getPasswordSecret());
		return new RegistryLoginFacade(getRegistryUrl(), getUserName(), password);
	}
	
}
