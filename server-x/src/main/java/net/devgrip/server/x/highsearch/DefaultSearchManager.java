package net.devgrip.server.x.highsearch;

import net.devgrip.commons.loader.ManagedSerializedForm;
import net.devgrip.commons.utils.ExceptionUtils;
import net.devgrip.server.AppServer;
import net.devgrip.server.cluster.ClusterManager;
import net.devgrip.server.entitymanager.ProjectManager;
import net.devgrip.server.event.Listen;
import net.devgrip.server.event.system.SystemStopped;
import net.devgrip.server.search.code.FieldConstants;
import net.devgrip.server.search.code.query.TooGeneralQueryException;
import net.devgrip.server.x.NoSubscriptionException;
import net.devgrip.server.x.highsearch.hit.HitContent;
import net.devgrip.server.x.highsearch.searchsupport.SearchBase;
import net.devgrip.server.x.subscription.XSubscriptionManager;
import org.apache.lucene.document.Document;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.index.LeafReaderContext;
import org.apache.lucene.search.*;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;

import javax.annotation.Nullable;
import javax.inject.Inject;
import javax.inject.Singleton;
import java.io.File;
import java.io.IOException;
import java.io.ObjectStreamException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@Singleton
public class DefaultSearchManager implements SearchManager, Serializable {
   private final ClusterManager clusterManager;
   private final ProjectManager projectManager;
   private final XSubscriptionManager subscriptionManager;
   private volatile SearcherManager searcherManager;

   @Inject
   public DefaultSearchManager(ClusterManager clusterManager, ProjectManager projectManager, XSubscriptionManager subscriptionManager) {
      this.clusterManager = clusterManager;
      this.projectManager = projectManager;
      this.subscriptionManager = subscriptionManager;
   }

   private File getIndexDir() {
      return new File(AppServer.getIndexDir(), "code");
   }

   @Nullable
   private SearcherManager getSearcherManager() {
       try {
           SearcherManager local = this.searcherManager;
           if (local == null) {
               synchronized (this) {
                   local = this.searcherManager;
                   if (local == null && this.getIndexDir().exists()) {
                       Directory directory = FSDirectory.open(this.getIndexDir().toPath());
                       if (DirectoryReader.indexExists(directory)) {
                           local = new SearcherManager(directory, null);
                           this.searcherManager = local;
                       }
                   }
               }
           }
           return local;
       } catch (IOException ioe) {
         throw ExceptionUtils.unchecked(ioe);
       }
   }

   @Override
   public List<HitContent> search(SearchBase query) throws TooGeneralQueryException {
	   if (!this.subscriptionManager.isSubscriptionActive()) {
		   throw new NoSubscriptionException();
	   }
       List<HitContent> matches = new ArrayList<>();
       Map<String, Collection<Long>> projectIdsByServer = this.projectManager.groupByActiveServers(query.getApplicableProjectIds());
       ArrayList<Entry<String, List<HitContent>>> entries = new ArrayList<>(this.clusterManager.runOnServers(projectIdsByServer.keySet(), () -> {
           SearcherManager searcherManager = this.getSearcherManager();
           if (searcherManager != null) {
               try {
                   final IndexSearcher searcher = searcherManager.acquire();

                   try {
                       final List<HitContent> innerMatches = new ArrayList<>();
                       final Collection<Long> projectIds = projectIdsByServer.get(this.clusterManager.getLocalServerAddress());
                       searcher.search(query.asLuceneQuery(projectIds), new SimpleCollector() {
                           private LeafReaderContext context;

                           public void collect(int doc) throws IOException {
                               Document document = searcher.doc(this.context.docBase + doc);
                               if (innerMatches.size() < query.getCount()) {
                                   long projectId = document.getField(FieldConstants.PROJECT_ID.name()).numericValue().longValue();
                                   if (projectIds.contains(projectId)) {
                                       HitContent match = query.matches(document, projectId);
                                       if (match != null) {
                                           innerMatches.add(match);
                                       }
                                   }
                               } else {
                                   throw new CollectionTerminatedException();
                               }
                           }

                           protected void doSetNextReader(LeafReaderContext context) throws IOException {
                               this.context = context;
                           }

                           public ScoreMode scoreMode() {
                               return ScoreMode.COMPLETE_NO_SCORES;
                           }
                       });
                       return innerMatches;
                   } finally {
                       searcherManager.release(searcher);
                   }
               } catch (IOException var12) {
                   throw new RuntimeException(var12);
               }
           } else {
               return new ArrayList<HitContent>();
           }
       }).entrySet());

       entries.sort(Entry.comparingByKey());

       for (Entry<String, List<HitContent>> result : entries) {
           matches.addAll(result.getValue());
       }

       if (matches.size() > query.getCount()) {
           matches = matches.subList(0, query.getCount());
       }

       return matches;
   }

   @Override
   public void indexUpdated() {
      if (this.searcherManager != null) {
         try {
            this.searcherManager.maybeRefresh();
         } catch (IOException var2) {
            throw ExceptionUtils.unchecked(var2);
         }
      }
   }

   @Listen
   public void on(SystemStopped ignore) {
      if (this.searcherManager != null) {
         try {
            this.searcherManager.close();
         } catch (IOException var3) {
            throw ExceptionUtils.unchecked(var3);
         }
      }
   }

   public Object writeReplace() throws ObjectStreamException {
      return new ManagedSerializedForm(SearchManager.class);
   }
}
