package net.devgrip.server.imports;

import net.devgrip.commons.utils.TaskLogger;
import net.devgrip.server.web.component.taskbutton.TaskResult;
import net.devgrip.server.web.util.ImportStep;

import java.io.Serializable;
import java.util.List;

public interface ProjectImporter extends Serializable {

	String getName();
	
	List<ImportStep<? extends Serializable>> getSteps();

	TaskResult doImport(boolean dryRun, TaskLogger logger);
	
}
