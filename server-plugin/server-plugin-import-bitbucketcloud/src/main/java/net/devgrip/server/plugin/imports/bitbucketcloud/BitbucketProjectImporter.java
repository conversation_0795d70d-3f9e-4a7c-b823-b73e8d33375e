package net.devgrip.server.plugin.imports.bitbucketcloud;

import com.google.common.collect.Lists;
import net.devgrip.commons.utils.TaskLogger;
import net.devgrip.server.imports.ProjectImporter;
import net.devgrip.server.web.component.taskbutton.TaskResult;
import net.devgrip.server.web.util.ImportStep;
import org.apache.wicket.model.ResourceModel;

import java.io.Serializable;
import java.util.List;

public class BitbucketProjectImporter implements ProjectImporter {

	private static final long serialVersionUID = 1L;
	
	private final ImportStep<ImportServer> serverStep = new ImportStep<ImportServer>() {

		private static final long serialVersionUID = 1L;

		@Override
		public String getTitle() {
			return new ResourceModel("BitbucketCloud.BitbucketProjectImporter.step1.title", "Authenticate to Bitbucket Cloud").getObject();
		}

		@Override
		protected ImportServer newSetting() {
			return new ImportServer();
		}
		
	};
	
	private final ImportStep<ImportRepositories> repositoriesStep = new ImportStep<ImportRepositories>() {

		private static final long serialVersionUID = 1L;

		@Override
		public String getTitle() {
			return new ResourceModel("BitbucketCloud.BitbucketProjectImporter.step2.title", "Specify repositories").getObject();
		}

		@Override
		protected ImportRepositories newSetting() {
			ImportRepositories repositories = new ImportRepositories();
			repositories.server = serverStep.getSetting();
			return repositories;
		}
		
	};
	
	private final ImportStep<ImportOption> optionStep = new ImportStep<ImportOption>() {

		private static final long serialVersionUID = 1L;

		@Override
		public String getTitle() {
			return new ResourceModel("BitbucketCloud.BitbucketProjectImporter.step3.title", "Specify import option").getObject();
		}

		@Override
		protected ImportOption newSetting() {
			return new ImportOption();
		}
		
	};
	
	@Override
	public String getName() {
		return BitbucketModule.NAME;
	}
	
	@Override
	public TaskResult doImport(boolean dryRun, TaskLogger logger) {
		ImportRepositories repositories = repositoriesStep.getSetting();
		ImportOption option = optionStep.getSetting();
		return serverStep.getSetting().importProjects(repositories, option, dryRun, logger);
	}

	@Override
	public List<ImportStep<? extends Serializable>> getSteps() {
		return Lists.newArrayList(serverStep, repositoriesStep, optionStep);
	}

}
