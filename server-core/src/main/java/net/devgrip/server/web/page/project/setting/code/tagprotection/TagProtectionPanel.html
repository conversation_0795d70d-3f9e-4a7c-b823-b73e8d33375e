<wicket:panel>
	<div class="tag-protection card card-default">
		<div class="card-header">
			<div class="card-title">
				<a class="toggle" onclick="$(this).closest('.tag-protection').toggleClass('expanded');"><wicket:svg href="arrow4" class="icon rotate-270"/></a>
				<span class="drag-indicator"><wicket:svg href="grip" class="icon mr-2"/></span>
				<span wicket:id="tags" class="mr-3"></span>
				<a wicket:id="edit" onclick="$(this).closest('.tag-protection').addClass('expanded');" class="btn btn-xs btn-icon btn-light btn-hover-primary mr-2" title="Edit this rule" wicket:message="title:BranchProtectionPanel.editThisRuleTitle"><wicket:svg href="edit" class="icon"></wicket:svg></a>
				<a wicket:id="delete" class="btn btn-xs btn-icon btn-light btn-hover-danger mr-4" title="Delete this rule" wicket:message="title:BranchProtectionPanel.deleteThisRuleTitle"><wicket:svg href="trash" class="icon"></wicket:svg></a>
				<span wicket:id="disabled" class="badge badge-warning"><wicket:message key="Disabled"></wicket:message></span>
			</div>
			<div class="card-toolbar">
				<span class="switch switch-sm switch-primary">
					<label>
						<input wicket:id="enable" type="checkbox"> 
					</label>
				</span>
			</div>
		</div>
		<div class="card-body">
			<div wicket:id="protection"></div>
		</div>
	</div>
</wicket:panel>
