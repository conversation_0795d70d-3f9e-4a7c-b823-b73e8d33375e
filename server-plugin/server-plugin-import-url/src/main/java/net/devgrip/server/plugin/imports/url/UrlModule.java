package net.devgrip.server.plugin.imports.url;

import com.google.common.collect.Lists;
import net.devgrip.commons.loader.AbstractPluginModule;
import net.devgrip.server.imports.ProjectImporter;
import net.devgrip.server.imports.ProjectImporterContribution;

import java.util.Collection;

/**
 * NOTE: Do not forget to rename moduleClass property defined in the pom if you've renamed this class.
 *
 */
public class UrlModule extends AbstractPluginModule {

	static final String NAME = "URL";
	
	@Override
	protected void configure() {
		super.configure();
		
		// put your guice bindings here
		contribute(ProjectImporterContribution.class, new ProjectImporterContribution() {

			@Override
			public Collection<ProjectImporter> getImporters() {
				return Lists.newArrayList(new UrlProjectImporter());
			}

			@Override
			public int getOrder() {
				return 100;
			}
			
		});
	}

}
