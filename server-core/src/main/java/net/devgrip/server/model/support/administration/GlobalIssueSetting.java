package net.devgrip.server.model.support.administration;

import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import net.devgrip.commons.utils.ExplicitException;
import net.devgrip.commons.utils.match.Matcher;
import net.devgrip.commons.utils.match.PathMatcher;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.buildspecmodel.inputspec.choiceinput.choiceprovider.Choice;
import net.devgrip.server.buildspecmodel.inputspec.choiceinput.choiceprovider.SpecifiedChoices;
import net.devgrip.server.model.Issue;
import net.devgrip.server.model.IssueSchedule;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.support.issue.*;
import net.devgrip.server.model.support.issue.field.spec.FieldSpec;
import net.devgrip.server.model.support.issue.field.spec.choicefield.ChoiceField;
import net.devgrip.server.model.support.issue.field.spec.choicefield.defaultvalueprovider.DefaultValue;
import net.devgrip.server.model.support.issue.field.spec.choicefield.defaultvalueprovider.SpecifiedDefaultValue;
import net.devgrip.server.model.support.issue.field.spec.userchoicefield.UserChoiceField;
import net.devgrip.server.model.support.issue.transitionspec.BranchUpdatedSpec;
import net.devgrip.server.model.support.issue.transitionspec.IssueStateTransitedSpec;
import net.devgrip.server.model.support.issue.transitionspec.ManualSpec;
import net.devgrip.server.model.support.issue.transitionspec.TransitionSpec;
import net.devgrip.server.search.entity.issue.IssueQuery;
import net.devgrip.server.search.entity.issue.IssueQueryParseOption;
import net.devgrip.server.util.patternset.PatternSet;
import net.devgrip.server.util.usage.Usage;
import net.devgrip.server.web.component.issue.workflowreconcile.*;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static net.devgrip.server.util.Colors.*;

@Editable
public class GlobalIssueSetting implements Serializable {
	
	private static final long serialVersionUID = 1L;

    public static final String PROP_EXTERNAL_ISSUE_PATTERN = "externalIssuePattern";

	private List<StateSpec> stateSpecs = new ArrayList<>();
	
	private List<TransitionSpec> transitionSpecs = new ArrayList<>();
	
	private List<FieldSpec> fieldSpecs = new ArrayList<>();
	
	private List<BoardSpec> boardSpecs = new ArrayList<>();
	
	private TimeTrackingSetting timeTrackingSetting = new TimeTrackingSetting();
	
	private List<String> listFields = new ArrayList<>();
	
	private List<String> listLinks = new ArrayList<>();
	
	private List<NamedIssueQuery> namedQueries = new ArrayList<>();
	
	private List<IssueTemplate> issueTemplates = new ArrayList<>();
	
	private CommitMessageFixPatterns commitMessageFixPatterns;

	private ExternalIssueTransformers externalIssueTransformers;

	private boolean reconciled = true;

	public GlobalIssueSetting(GlobalIssueSettingConfig config) {
		initAndConfigure(config);
		initCommitMessageFixPatterns();
		initExternalIssueTransformers();
	}

	private void initCommitMessageFixPatterns() {
		commitMessageFixPatterns = new CommitMessageFixPatterns();
		var entry = new CommitMessageFixPatterns.Entry();
		entry.setPrefix("(^|\\W)(fix|fixed|fixes|fixing|resolve|resolved|resolves|resolving|close|closed|closes|closing)[\\s:]+");
		entry.setSuffix("(?=$|\\W)");
		commitMessageFixPatterns.getEntries().add(entry);
		entry = new CommitMessageFixPatterns.Entry();
		entry.setPrefix("\\(\\s*");
		entry.setSuffix("\\s*\\)\\s*$");
		commitMessageFixPatterns.getEntries().add(entry);
	}

	private void initExternalIssueTransformers() {
		externalIssueTransformers = new ExternalIssueTransformers();
	}

	private void initAndConfigure(GlobalIssueSettingConfig config) {
		ChoiceField type = new ChoiceField();
		String typeName = config.getTypeName();
		type.setName(typeName);
		SpecifiedChoices specifiedChoices = new SpecifiedChoices();

		List<Choice> choices = new ArrayList<>();
		Choice newFeature = new Choice();
		newFeature.setValue(config.getTypeValueNewFeature());
		newFeature.setColor(ISSUE_TYPE_COLOR_NEW_FEATURE);
		choices.add(newFeature);

		Choice improvement = new Choice();
		improvement.setValue(config.getTypeValueImprovement());
		improvement.setColor(ISSUE_TYPE_COLOR_IMPROVEMENT);
		choices.add(improvement);

		Choice bug = new Choice();
		bug.setValue(config.getTypeValueBug());
		bug.setColor(ISSUE_TYPE_COLOR_BUG);
		choices.add(bug);

		Choice task = new Choice();
		task.setValue(config.getTypeValueTask());
		task.setColor(ISSUE_TYPE_COLOR_TASK);
		choices.add(task);

		specifiedChoices.setChoices(choices);
		type.setChoiceProvider(specifiedChoices);

		SpecifiedDefaultValue specifiedDefaultValue = new SpecifiedDefaultValue();
		DefaultValue defaultValue = new DefaultValue();
		defaultValue.setValue(config.getTypeValueNewFeature());
		specifiedDefaultValue.getDefaultValues().add(defaultValue);

		type.setDefaultValueProvider(specifiedDefaultValue);

		fieldSpecs.add(type);

		ChoiceField priority = new ChoiceField();
		String priorityName = config.getPriorityName();
		priority.setName(priorityName);
		specifiedChoices = new SpecifiedChoices();

		choices = new ArrayList<>();

		Choice minor = new Choice();
		minor.setValue(config.getPriorityValueMinor());
		minor.setColor(ISSUE_PRIORITY_COLOR_MINOR);
		choices.add(minor);

		Choice normal = new Choice();
		normal.setValue(config.getPriorityValueNormal());
		normal.setColor(ISSUE_PRIORITY_COLOR_NORMAL);
		choices.add(normal);

		Choice major = new Choice();
		major.setValue(config.getPriorityValueMajor());
		major.setColor(ISSUE_PRIORITY_COLOR_MAJOR);
		choices.add(major);

		Choice critical = new Choice();
		critical.setValue(config.getPriorityValueCritical());
		critical.setColor(ISSUE_PRIORITY_COLOR_CRITICAL);
		choices.add(critical);

		specifiedChoices.setChoices(choices);
		priority.setChoiceProvider(specifiedChoices);

		specifiedDefaultValue = new SpecifiedDefaultValue();
		defaultValue = new DefaultValue();
		defaultValue.setValue(config.getPriorityValueNormal());
		specifiedDefaultValue.getDefaultValues().add(defaultValue);
		priority.setDefaultValueProvider(specifiedDefaultValue);

		fieldSpecs.add(priority);

		UserChoiceField assignee = new UserChoiceField();
		assignee.setAllowEmpty(true);
		assignee.setNameOfEmptyValue(config.getAssigneeValueEmpty());
		String assigneeName = config.getAssigneeName();
		assignee.setName(assigneeName);

		fieldSpecs.add(assignee);

		String stateValueOpen = config.getStateValueOpen();
		String stateValueClosed = config.getStateValueClosed();
		String stateValueInProgress = config.getStateValueInProgress();
		String linkChildIssue = config.getLinkChildIssue();
		String linkParentIssue = config.getLinkParentIssue();
		String linkDuplicates = config.getLinkDuplicates();
		String linkRelatesTo = config.getLinkRelatesTo();
		StateSpec open = new StateSpec();
		open.setName(stateValueOpen);
		open.setColor(ISSUE_STATE_COLOR_OPEN);

		stateSpecs.add(open);

		StateSpec closed = new StateSpec();
		closed.setColor(ISSUE_STATE_COLOR_CLOSE);
		closed.setName(stateValueClosed);

		stateSpecs.add(closed);

		StateSpec inProgress = new StateSpec();
		inProgress.setColor(ISSUE_STATE_COLOR_IN_PROGRESS);
		inProgress.setName(stateValueInProgress);

		stateSpecs.add(inProgress);

		var manualSpec = new ManualSpec();
		manualSpec.setFromStates(Lists.newArrayList(stateValueOpen,stateValueInProgress));
		manualSpec.setToStates(Lists.newArrayList(stateValueInProgress,stateValueClosed));
		manualSpec.setAuthorizedRoles(Lists.newArrayList(config.getRoleCodeWriter(), config.getRoleIssueManager(),ManualSpec.ROLE_SUBMITTER, String.format("{%s}", assigneeName)));
		manualSpec.setIssueQuery(String.format("not(any \"%s\" matching(\"State\" is \"%s\"))", linkChildIssue, stateValueOpen));

		transitionSpecs.add(manualSpec);

		var branchUpdatedSpec = new BranchUpdatedSpec();
		branchUpdatedSpec.setFromStates(Lists.newArrayList(stateValueOpen,stateValueInProgress));
		branchUpdatedSpec.setToState(stateValueClosed);
		branchUpdatedSpec.setBranches("main");

		transitionSpecs.add(branchUpdatedSpec);

		var issueStateTransitedSpec = new IssueStateTransitedSpec();
		issueStateTransitedSpec.setFromStates(Lists.newArrayList(stateValueOpen,stateValueInProgress));
		issueStateTransitedSpec.setToState(stateValueClosed);
		issueStateTransitedSpec.setStates(Lists.newArrayList(stateValueClosed));
		issueStateTransitedSpec.setIssueQuery(String.format("any \"%s\" matching(current issue) and all \"%s\" matching(\"State\" is \"%s\")", linkChildIssue, linkChildIssue, stateValueClosed));

		transitionSpecs.add(issueStateTransitedSpec);

		manualSpec = new ManualSpec();
		manualSpec.setFromStates(Lists.newArrayList(stateValueClosed));
		manualSpec.setToStates(Lists.newArrayList(stateValueOpen));
		manualSpec.setAuthorizedRoles(Lists.newArrayList(config.getRoleIssueManager(), config.getRoleIssueReporter()));

		transitionSpecs.add(manualSpec);

		issueStateTransitedSpec = new IssueStateTransitedSpec();
		issueStateTransitedSpec.setFromStates(Lists.newArrayList(stateValueClosed));
		issueStateTransitedSpec.setToState(stateValueOpen);
		issueStateTransitedSpec.setStates(Lists.newArrayList(stateValueOpen));
		issueStateTransitedSpec.setIssueQuery(String.format("any \"%s\" matching(current issue)", linkChildIssue));

		transitionSpecs.add(issueStateTransitedSpec);

		manualSpec = new ManualSpec();
		manualSpec.setAuthorizedRoles(Lists.newArrayList(config.getRoleIssueManager()));

		transitionSpecs.add(manualSpec);

		BoardSpec board = new BoardSpec();
		board.setName(config.getDefaultBoardName());
		board.setIdentifyField(Issue.NAME_STATE);
		board.setBacklogBaseQuery(String.format("\"State\" is \"%s\"", stateValueOpen));
		board.setColumns(Lists.newArrayList(stateValueOpen, stateValueInProgress, stateValueClosed));
		board.setDisplayFields(Lists.newArrayList(Issue.NAME_STATE, typeName, priorityName, assigneeName, IssueSchedule.NAME_ITERATION));
		board.setDisplayLinks(Lists.newArrayList(linkChildIssue, linkParentIssue, linkDuplicates, linkRelatesTo));
		boardSpecs.add(board);

		listFields.add(Issue.NAME_STATE);
		listFields.add(typeName);
		listFields.add(priorityName);
		listFields.add(assigneeName);
		listFields.add(IssueSchedule.NAME_ITERATION);

		listLinks.add(linkChildIssue);
		listLinks.add(linkParentIssue);
		listLinks.add(linkDuplicates);
		listLinks.add(linkRelatesTo);

		namedQueries.add(new NamedIssueQuery(config.getNqOpen(), config.getNqOpenQuery()));
		namedQueries.add(new NamedIssueQuery(config.getNqAssignedToMeAndOpen(), config.getNqAssignedToMeAndOpenQuery()));
		namedQueries.add(new NamedIssueQuery(config.getNqSubmittedByMeAndOpen(), config.getNqSubmittedByMeAndOpenQuery()));
		namedQueries.add(new NamedIssueQuery(config.getNqAssignedToMe(), config.getNqAssignedToMeQuery()));
		namedQueries.add(new NamedIssueQuery(config.getNqSubmittedByMe(), "submitted by me"));
		namedQueries.add(new NamedIssueQuery(config.getNqSubmittedRecently(), "\"Submit Date\" is since \"last week\""));
		namedQueries.add(new NamedIssueQuery(config.getNqMentionedMe(), "mentioned me"));
		namedQueries.add(new NamedIssueQuery(config.getNqHasActivityRecently(), "\"Last Activity Date\" is since \"last week\""));
		namedQueries.add(new NamedIssueQuery(config.getNqOpenAndCritical(), config.getNqOpenAndCriticalQuery()));
		namedQueries.add(new NamedIssueQuery(config.getNqOpenAndUnassigned(), config.getNqOpenAndUnassignedQuery()));
		namedQueries.add(new NamedIssueQuery(config.getNqOpenAndUnscheduled(), config.getNqOpenAndUnscheduledQuery()));
		namedQueries.add(new NamedIssueQuery(config.getNqClosed(), config.getNqClosedQuery()));
		namedQueries.add(new NamedIssueQuery(config.getNqAll(), null));

		timeTrackingSetting.setAggregationLink(linkChildIssue);
	}
	
	public List<String> sortFieldNames(Collection<String> fieldNames) {
		List<String> sorted = new ArrayList<>(fieldNames);
		List<String> allFieldNames = getFieldNames();
		Collections.sort(sorted, new Comparator<String>() {

			@Override
			public int compare(String o1, String o2) {
				return allFieldNames.indexOf(o1) - allFieldNames.indexOf(o2);
			}
			
		});
		return sorted;
	}
	
	public List<StateSpec> getStateSpecs() {
		return stateSpecs;
	}

	public void setStateSpecs(List<StateSpec> stateSpecs) {
		this.stateSpecs = stateSpecs;
	}

	public List<TransitionSpec> getTransitionSpecs() {
		return transitionSpecs;
	}

	public void setTransitionSpecs(List<TransitionSpec> transitionSpecs) {
		this.transitionSpecs = transitionSpecs;
	}

	@Editable
	public List<FieldSpec> getFieldSpecs() {
		return fieldSpecs;
	}

	public void setFieldSpecs(List<FieldSpec> fieldSpecs) {
		this.fieldSpecs = fieldSpecs;
	}

	public boolean isReconciled() {
		return reconciled;
	}

	public void setReconciled(boolean reconciled) {
		this.reconciled = reconciled;
	}

	public Map<String, FieldSpec> getFieldSpecMap(@Nullable Collection<String> fieldNames) {
		Map<String, FieldSpec> fieldSpecMap = new LinkedHashMap<>();
		for (FieldSpec fieldSpec: getFieldSpecs()) {
			if (fieldNames == null || fieldNames.contains(fieldSpec.getName()))
				fieldSpecMap.put(fieldSpec.getName(), fieldSpec);
		}
		return fieldSpecMap;
	}
	
	public Map<String, StateSpec> getStateSpecMap() {
		Map<String, StateSpec> stateSpecMap = new LinkedHashMap<>();
		for (StateSpec state: getStateSpecs())
			stateSpecMap.put(state.getName(), state);
		return stateSpecMap;
	}
	
	public List<String> getFieldNames() {
		return new ArrayList<>(getFieldSpecMap(null).keySet());
	}
	
	@Nullable
	public StateSpec getStateSpec(String stateName) {
		return getStateSpecMap().get(stateName);
	}

	@Nullable
	public FieldSpec getFieldSpec(String fieldName) {
		return getFieldSpecMap(null).get(fieldName);
	}
	
	public Collection<String> getUndefinedStates() {
		Collection<String> undefinedStates = new HashSet<>();
		for (TransitionSpec transition: getTransitionSpecs())
			undefinedStates.addAll(transition.getUndefinedStates());
		for (BoardSpec board: getBoardSpecs())
			undefinedStates.addAll(board.getUndefinedStates());
		for (IssueTemplate template: getIssueTemplates())
			undefinedStates.addAll(template.getQueryUpdater().getUndefinedStates());
		
		IssueQueryParseOption option = new IssueQueryParseOption().enableAll(true);
		for (NamedIssueQuery namedQuery: getNamedQueries()) {
			try {
				IssueQuery query = IssueQuery.parse(null, namedQuery.getQuery(), option, false);
				undefinedStates.addAll(query.getUndefinedStates());
			} catch (Exception e) {
			}
		}
		return undefinedStates;
	}
	
	public Collection<String> getUndefinedFields() {
		Collection<String> undefinedFields = new HashSet<>();
		for (String fieldName: getListFields()) {
			if (!fieldName.equals(Issue.NAME_STATE) 
					&& !fieldName.equals(IssueSchedule.NAME_ITERATION) 
					&& getFieldSpec(fieldName) == null) {
				undefinedFields.add(fieldName);
			}
		}
		
		for (TransitionSpec transition: getTransitionSpecs())
			undefinedFields.addAll(transition.getUndefinedFields());
		for (FieldSpec field: getFieldSpecs())
			undefinedFields.addAll(field.getUndefinedFields());
		for (BoardSpec board: getBoardSpecs())
			undefinedFields.addAll(board.getUndefinedFields());
		for (IssueTemplate template: getIssueTemplates())
			undefinedFields.addAll(template.getQueryUpdater().getUndefinedFields());
		
		IssueQueryParseOption option = new IssueQueryParseOption().enableAll(true);
		
		for (NamedIssueQuery namedQuery: getNamedQueries()) {
			try {
				IssueQuery query = IssueQuery.parse(null, namedQuery.getQuery(), option, false);
				undefinedFields.addAll(query.getUndefinedFields());
			} catch (Exception e) {
			}
		}
		return undefinedFields;
	}
	
	public Collection<UndefinedFieldValue> getUndefinedFieldValues() {
		Collection<UndefinedFieldValue> undefinedFieldValues = new HashSet<>();
		for (TransitionSpec transition: getTransitionSpecs())
			undefinedFieldValues.addAll(transition.getUndefinedFieldValues());
		for (FieldSpec field: getFieldSpecs())
			undefinedFieldValues.addAll(field.getUndefinedFieldValues());
		for (BoardSpec board: getBoardSpecs())
			undefinedFieldValues.addAll(board.getUndefinedFieldValues());
		for (IssueTemplate template: getIssueTemplates())
			undefinedFieldValues.addAll(template.getQueryUpdater().getUndefinedFieldValues());

		IssueQueryParseOption option = new IssueQueryParseOption().enableAll(true);
		for (NamedIssueQuery namedQuery: getNamedQueries()) {
			try {
				IssueQuery query = IssueQuery.parse(null, namedQuery.getQuery(), option, false);
				undefinedFieldValues.addAll(query.getUndefinedFieldValues());
			} catch (Exception e) {
			}
		}
		return undefinedFieldValues;
	}
	
	public void fixUndefinedStates(Map<String, UndefinedStateResolution> resolutions) {
		for (Iterator<TransitionSpec> it = getTransitionSpecs().iterator(); it.hasNext();) {
			if (!it.next().fixUndefinedStates(resolutions))
				it.remove();
		}
		for (Iterator<BoardSpec> it = getBoardSpecs().iterator(); it.hasNext();) {
			if (!it.next().fixUndefinedStates(resolutions))
				it.remove();
		}
		for (Iterator<IssueTemplate> it = getIssueTemplates().iterator(); it.hasNext();) {
			if (!it.next().getQueryUpdater().fixUndefinedStates(resolutions))
				it.remove();
		}
		
		IssueQueryParseOption option = new IssueQueryParseOption().enableAll(true);
		for (Iterator<NamedIssueQuery> it = getNamedQueries().iterator(); it.hasNext();) {
			NamedIssueQuery namedQuery = it.next();
			try {
				IssueQuery query = IssueQuery.parse(null, namedQuery.getQuery(), option, false);
				if (query.fixUndefinedStates(resolutions))
					namedQuery.setQuery(query.toString());
				else
					it.remove();
			} catch (Exception e) {
			}
		}
	}

	public Collection<String> fixUndefinedFields(Map<String, UndefinedFieldResolution> resolutions) {
		for (Map.Entry<String, UndefinedFieldResolution> entry: resolutions.entrySet()) {
			if (entry.getValue().getFixType() == UndefinedFieldResolution.FixType.CHANGE_TO_ANOTHER_FIELD)  
				ReconcileUtils.renameItem(listFields, entry.getKey(), entry.getValue().getNewField());
			else 
				listFields.remove(entry.getKey());
		}
		
		for (Iterator<TransitionSpec> it = getTransitionSpecs().iterator(); it.hasNext();) {
			if (!it.next().fixUndefinedFields(resolutions))
				it.remove();
		}
		for (Iterator<BoardSpec> it = getBoardSpecs().iterator(); it.hasNext();) {
			if (!it.next().fixUndefinedFields(resolutions))
				it.remove();
		}
		for (Iterator<IssueTemplate> it = getIssueTemplates().iterator(); it.hasNext();) {
			if (!it.next().getQueryUpdater().fixUndefinedFields(resolutions))
				it.remove();
		}
		
		IssueQueryParseOption option = new IssueQueryParseOption().enableAll(true);
		for (Iterator<NamedIssueQuery> it = getNamedQueries().iterator(); it.hasNext();) {
			NamedIssueQuery namedQuery = it.next();
			try {
				IssueQuery query = IssueQuery.parse(null, namedQuery.getQuery(), option, false);
				if (query.fixUndefinedFields(resolutions))
					namedQuery.setQuery(query.toString());
				else
					it.remove();
			} catch (Exception e) {
			}
		}
		
		Collection<String> derivedDeletions = new HashSet<>();
		for (Iterator<FieldSpec> it = getFieldSpecs().iterator(); it.hasNext();) {
			FieldSpec field = it.next();
			if (!field.fixUndefinedFields(resolutions)) {
				it.remove();
				derivedDeletions.add(field.getName());
			}
		}

		return derivedDeletions;
	}
	
	public Collection<String> fixUndefinedFieldValues(Map<String, UndefinedFieldValuesResolution> resolutions) {
		for (Iterator<TransitionSpec> it = getTransitionSpecs().iterator(); it.hasNext();) {
			if (!it.next().fixUndefinedFieldValues(resolutions))
				it.remove();
		}
		for (Iterator<BoardSpec> it = getBoardSpecs().iterator(); it.hasNext();) {
			if (!it.next().fixUndefinedFieldValues(resolutions))
				it.remove();
		}
		for (Iterator<IssueTemplate> it = getIssueTemplates().iterator(); it.hasNext();) {
			if (!it.next().getQueryUpdater().fixUndefinedFieldValues(resolutions))
				it.remove();
		}
		
		IssueQueryParseOption option = new IssueQueryParseOption().enableAll(true);
		for (Iterator<NamedIssueQuery> it = getNamedQueries().iterator(); it.hasNext();) {
			NamedIssueQuery namedQuery = it.next();
			try {
				IssueQuery query = IssueQuery.parse(null, namedQuery.getQuery(), option, false);
				if (query.fixUndefinedFieldValues(resolutions))
					namedQuery.setQuery(query.toString());
				else
					it.remove();
			} catch (Exception e) {
			}
		}
		
		Collection<String> derivedDeletions = new HashSet<>();
		for (Iterator<FieldSpec> it = getFieldSpecs().iterator(); it.hasNext();) {
			FieldSpec field = it.next();
			if (!field.fixUndefinedFieldValues(resolutions)) {
				it.remove();
				derivedDeletions.add(field.getName());
			}
		}

		return derivedDeletions;
	}
	
	public void onRenameUser(String oldName, String newName) {
		for (FieldSpec field: getFieldSpecs())
			field.onRenameUser(oldName, newName);
		for (BoardSpec board: getBoardSpecs())
			board.onRenameUser(this, oldName, newName);
	}
	
	public Usage onDeleteUser(String userName) {
		Usage usage = new Usage();
		for (Iterator<BoardSpec> it = getBoardSpecs().iterator(); it.hasNext();) { 
			if (it.next().onDeleteUser(this, userName))
				it.remove();
		}
		for (FieldSpec field: getFieldSpecs())
			usage.add(field.onDeleteUser(userName));
		
		return usage.prefix("issue settings");
	}
	
	public void onRenameGroup(String oldName, String newName) {
		for (FieldSpec field: getFieldSpecs())
			field.onRenameGroup(oldName, newName);
	}

	public Usage onDeleteGroup(String groupName) {
		Usage usage = new Usage();
		
		for (FieldSpec field: getFieldSpecs())
			usage.add(field.onDeleteGroup(groupName));
		
		return usage.prefix("issue settings");
	}
	
	public void onMoveProject(String oldPath, String newPath) {
		for (FieldSpec field: getFieldSpecs()) 
			field.onMoveProject(oldPath, newPath);
		
		for (TransitionSpec transition: getTransitionSpecs())
			transition.onMoveProject(oldPath, newPath);
		for (BoardSpec board: getBoardSpecs()) {
			board.getBaseQueryUpdater().onMoveProject(oldPath, newPath);
			board.getBacklogBaseQueryUpdater().onMoveProject(oldPath, newPath);
		}
		
		for (IssueTemplate template: getIssueTemplates())
			template.getQueryUpdater().onMoveProject(oldPath, newPath);
	}
	
	public Usage onDeleteProject(String projectPath) {
		Usage usage = new Usage();
		
		for (FieldSpec field: getFieldSpecs()) 
			usage.add(field.onDeleteProject(projectPath));
		
		int index = 1;
		for (TransitionSpec transition: getTransitionSpecs()) 
			usage.add(transition.onDeleteProject(projectPath).prefix("state transition #" + index++));
		
		index = 1;
		for (BoardSpec board: getBoardSpecs()) { 
			usage.add(board.getBaseQueryUpdater().onDeleteProject(projectPath).prefix("default board #" + index));
			usage.add(board.getBacklogBaseQueryUpdater().onDeleteProject(projectPath).prefix("default board #" + index));
			index++;
		}
		
		index = 1;
		for (IssueTemplate template: getIssueTemplates()) 
			usage.add(template.getQueryUpdater().onDeleteProject(projectPath).prefix("description template #" + index++));
		
		return usage.prefix("issue settings");
		
	}
	
	public void onRenameRole(String oldName, String newName) {
		for (TransitionSpec transition: getTransitionSpecs())
			transition.onRenameRole(oldName, newName);
	}
	
	public Usage onDeleteRole(String roleName) {
		Usage usage = new Usage();
		
		int index = 1;
		for (TransitionSpec transition: getTransitionSpecs())
			usage.add(transition.onDeleteRole(roleName).prefix("state transition #" + index++));
		
		return usage.prefix("issue settings");
	}
	
	public void onRenameLink(String oldName, String newName) {
		for (TransitionSpec transition: getTransitionSpecs())
			transition.onRenameLink(oldName, newName);
		for (BoardSpec board: getBoardSpecs()) {
			board.getBaseQueryUpdater().onRenameLink(oldName, newName);
			board.getBacklogBaseQueryUpdater().onRenameLink(oldName, newName);
			ReconcileUtils.renameItem(board.getDisplayLinks(), oldName, newName);
		}
		
		for (IssueTemplate template: getIssueTemplates())
			template.getQueryUpdater().onRenameLink(oldName, newName);
		
		ReconcileUtils.renameItem(listLinks, oldName, newName);
		
		timeTrackingSetting.onRenameLink(oldName, newName);
	}

	public Usage onDeleteLink(String linkName) {
		Usage usage = new Usage();
		
		int index = 1;
		for (TransitionSpec transition: getTransitionSpecs()) 
			usage.add(transition.onDeleteLink(linkName).prefix("state transition #" + index++));
		
		index = 1;
		for (BoardSpec board: getBoardSpecs()) { 
			usage.add(board.getBaseQueryUpdater().onDeleteLink(linkName).prefix("default board #" + index));
			usage.add(board.getBacklogBaseQueryUpdater().onDeleteLink(linkName).prefix("default board #" + index));
			if (board.getDisplayLinks().contains(linkName))
				usage.add(new Usage().add("display links").prefix("default board #" + index));
			index++;
		}
		
		index = 1;
		for (IssueTemplate template: getIssueTemplates()) 
			usage.add(template.getQueryUpdater().onDeleteLink(linkName).prefix("description template #" + index++));
		
		if (listLinks.contains(linkName))
			usage.add(new Usage().add("fields & links").prefix("-> issues"));
		
		usage.add(timeTrackingSetting.onDeleteLink(linkName).prefix("time tracking"));
		
		return usage.prefix("issue settings");
	}
	
	public StateSpec getInitialStateSpec() {
		if (!getStateSpecs().isEmpty())
			return getStateSpecs().iterator().next();
		else
			throw new ExplicitException("No any issue state is defined");
	}
	
	public List<BoardSpec> getBoardSpecs() {
		return boardSpecs;
	}

	public void setBoardSpecs(List<BoardSpec> boardSpecs) {
		this.boardSpecs = boardSpecs;
	}

	public TimeTrackingSetting getTimeTrackingSetting() {
		return timeTrackingSetting;
	}

	public void setTimeTrackingSetting(TimeTrackingSetting timeTrackingSetting) {
		this.timeTrackingSetting = timeTrackingSetting;
	}

	public List<String> getListFields() {
		return listFields;
	}

	public void setListFields(List<String> listFields) {
		this.listFields = listFields;
	}
	
	public List<String> getListLinks() {
		return listLinks;
	}

	public void setListLinks(List<String> listLinks) {
		this.listLinks = listLinks;
	}

	public List<NamedIssueQuery> getNamedQueries() {
		return namedQueries;
	}

	public void setNamedQueries(List<NamedIssueQuery> namedQueries) {
		this.namedQueries = namedQueries;
	}
	
	public List<IssueTemplate> getIssueTemplates() {
		return issueTemplates;
	}

	public void setIssueTemplates(List<IssueTemplate> issueTemplates) {
		this.issueTemplates = issueTemplates;
	}

	public CommitMessageFixPatterns getCommitMessageFixPatterns() {
		return commitMessageFixPatterns;
	}

	public void setCommitMessageFixPatterns(CommitMessageFixPatterns commitMessageFixPatterns) {
		this.commitMessageFixPatterns = commitMessageFixPatterns;
	}

	public ExternalIssueTransformers getExternalIssueTransformers() {
		return externalIssueTransformers;
	}

	public void setExternalIssueTransformers(ExternalIssueTransformers externalIssueTransformers) {
		this.externalIssueTransformers = externalIssueTransformers;
	}

	@Nullable
	public NamedIssueQuery getNamedQuery(String name) {
		for (NamedIssueQuery namedQuery: getNamedQueries()) {
			if (namedQuery.getName().equals(name))
				return namedQuery;
		}
		return null;
	}
	
	public Collection<String> getPromptFieldsUponIssueOpen(Project project) {
		Matcher matcher = new PathMatcher();
		return getFieldSpecs().stream()
				.filter(it->it.isPromptUponIssueOpen() && (it.getApplicableProjects() == null || PatternSet.parse(it.getApplicableProjects()).matches(matcher, project.getPath())))
				.map(it->it.getName())
				.collect(Collectors.toList());
	}
	
	public int getStateOrdinal(String state) {
		return getStateSpecs().indexOf(getStateSpec(state));
	}
	
}
