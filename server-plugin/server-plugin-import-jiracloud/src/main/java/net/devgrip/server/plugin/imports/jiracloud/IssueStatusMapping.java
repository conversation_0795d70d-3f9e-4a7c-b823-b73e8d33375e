package net.devgrip.server.plugin.imports.jiracloud;

import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.ChoiceProvider;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.model.support.administration.GlobalIssueSetting;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Editable
public class IssueStatusMapping implements Serializable {

	private static final long serialVersionUID = 1L;

	private String jiraIssueStatus;
	
	private String oneDevIssueState;

	@Editable(order=100, name="IssueStatusMapping.issuePriority.name")
	@NotEmpty
	public String getJiraIssueStatus() {
		return jiraIssueStatus;
	}

	public void setJiraIssueStatus(String jiraIssueStatus) {
		this.jiraIssueStatus = jiraIssueStatus;
	}

	@Editable(order=200, name="IssueStatusMapping.issueState.name", description="IssueStatusMapping.issueState.desc")
	@ChoiceProvider("getOneDevIssueStateChoices")
	@NotEmpty
	public String getOneDevIssueState() {
		return oneDevIssueState;
	}

	public void setOneDevIssueState(String oneDevIssueState) {
		this.oneDevIssueState = oneDevIssueState;
	}

	@SuppressWarnings("unused")
	private static List<String> getOneDevIssueStateChoices() {
		GlobalIssueSetting issueSetting = AppServer.getInstance(SettingManager.class).getIssueSetting();
		return issueSetting.getStateSpecs().stream().map(it->it.getName()).collect(Collectors.toList());
	}
	
}
