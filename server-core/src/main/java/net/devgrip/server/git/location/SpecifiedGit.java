package net.devgrip.server.git.location;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;

import javax.validation.constraints.NotEmpty;

@Editable(name="Use_Specified_Git", order=200)
public class SpecifiedGit extends GitLocation {

	private static final long serialVersionUID = 1L;
	
	private String gitPath;
	
	@Editable(name = "Specified_Git_Path_Name", description="Specified_Git_Path_Desc")
	@OmitName
	@NotEmpty
	public String getGitPath() {
		return gitPath;
	}

	public void setGitPath(String gitPath) {
		this.gitPath = gitPath;
	}

	@Override
	public String getExecutable() {
		return gitPath;
	}

}
