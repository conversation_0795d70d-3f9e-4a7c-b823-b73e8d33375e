package net.devgrip.server.x.highsearch;

import net.devgrip.commons.codeassist.InputSuggestion;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Patterns;
import net.devgrip.server.security.permission.ReadCode;
import net.devgrip.server.web.util.SuggestionUtils;

import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Editable
public class ProjectsBean implements Serializable {

	private String projects;

	public ProjectsBean() {
	}

	@Editable(
		name = "In_Projects",
		order = 100,
		placeholder = "In_Projects_Placeholder",
		description = "In_Projects_Desc"
	)
	@Patterns(
		suggester = "suggestProjects",
		path = true
	)
	public String getProjects() {
		return this.projects;
	}

	public void setProjects(String projects) {
		this.projects = projects;
	}

	private static List<InputSuggestion> suggestProjects(String matchWith) {
		return SuggestionUtils.suggestProjectPaths(matchWith, new ReadCode());
	}
}
