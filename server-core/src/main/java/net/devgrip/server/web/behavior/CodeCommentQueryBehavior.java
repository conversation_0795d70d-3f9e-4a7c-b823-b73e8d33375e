package net.devgrip.server.web.behavior;

import com.google.common.base.Optional;
import com.google.common.base.Preconditions;
import net.devgrip.commons.codeassist.FenceAware;
import net.devgrip.commons.codeassist.InputCompletion;
import net.devgrip.commons.codeassist.InputSuggestion;
import net.devgrip.commons.codeassist.grammar.LexerRuleRefElementSpec;
import net.devgrip.commons.codeassist.parser.Element;
import net.devgrip.commons.codeassist.parser.ParseExpect;
import net.devgrip.commons.codeassist.parser.TerminalExpect;
import net.devgrip.commons.utils.ExplicitException;
import net.devgrip.server.model.CodeComment;
import net.devgrip.server.model.Project;
import net.devgrip.server.search.entity.codecomment.CodeCommentQuery;
import net.devgrip.server.search.entity.codecomment.CodeCommentQueryParser;
import net.devgrip.server.search.entity.project.ProjectQuery;
import net.devgrip.server.web.behavior.describer.CodeCommentQueryDescriber;
import net.devgrip.server.web.behavior.describer.Describer;
import net.devgrip.server.web.behavior.describer.OperatorDescriber;
import net.devgrip.server.web.behavior.inputassist.ANTLRAssistBehavior;
import net.devgrip.server.web.util.SuggestionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.model.IModel;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;

import static net.devgrip.server.search.entity.codecomment.CodeCommentQuery.getRuleName;
import static net.devgrip.server.search.entity.codecomment.CodeCommentQueryLexer.*;
import static net.devgrip.server.util.Hints.HINT_Escape_Quotes;
import static net.devgrip.server.util.Hints.HINT_Name_Wildcard;

public class CodeCommentQueryBehavior extends ANTLRAssistBehavior {

	private static final String FUZZY_SUGGESTION_DESCRIPTION_PREFIX = "enclose with ~";
	
	private final IModel<Project> projectModel;
	
	private final boolean withCurrentUserCriteria;
	
	private final boolean withOrder;
	
	public CodeCommentQueryBehavior(IModel<Project> projectModel, boolean withCurrentUserCriteria, boolean withOrder) {
		super(CodeCommentQueryParser.class, "query", false);
		this.projectModel = projectModel;
		this.withCurrentUserCriteria = withCurrentUserCriteria;
		this.withOrder = withOrder;
	}
	
	@Override
	public void detach(Component component) {
		super.detach(component);
		projectModel.detach();
	}
	
	private Project getProject() {
		return projectModel.getObject();
	}
	
	@Override
	protected List<InputSuggestion> suggest(TerminalExpect terminalExpect) {
		if (terminalExpect.getElementSpec() instanceof LexerRuleRefElementSpec) {
			LexerRuleRefElementSpec spec = (LexerRuleRefElementSpec) terminalExpect.getElementSpec();
			if (spec.getRuleName().equals("Quoted")) {
				return new FenceAware(codeAssist.getGrammar(), '"', '"') {

					@Override
					protected List<InputSuggestion> match(String matchWith) {
						Project project = getProject();
						ParseExpect criteriaValueExpect;
						if ("criteriaField".equals(spec.getLabel())) {
							return SuggestionUtils.suggest(getQueryOrOrderFieldsCandidates(CodeComment.QUERY_FIELDS), matchWith);
						} else if ("orderField".equals(spec.getLabel())) {
							return SuggestionUtils.suggest(getQueryOrOrderFieldsCandidates(CodeComment.SORT_FIELDS.keySet()), matchWith);
						} else if ((criteriaValueExpect = terminalExpect.findExpectByLabel("criteriaValue")) != null) {
							List<Element> fieldElements = criteriaValueExpect.getState().findMatchedElementsByLabel("criteriaField", true);
							List<Element> operatorElements = criteriaValueExpect.getState().findMatchedElementsByLabel("operator", true);
							Preconditions.checkState(operatorElements.size() == 1);
							String operatorName = StringUtils.normalizeSpace(operatorElements.get(0).getMatchedText());
							int operator = CodeCommentQuery.getOperator(operatorName);							
							if (fieldElements.isEmpty()) {
								if (operator == Mentioned || operator == CreatedBy || operator == RepliedBy) 
									return SuggestionUtils.suggestUsers(matchWith);
								else 
									return null;
							} else {
								String fieldName = CodeCommentQuery.getValue(fieldElements.get(0).getMatchedText());
								try {
									CodeCommentQuery.checkField(project, fieldName, operator);
									if (fieldName.equals(CodeComment.NAME_CREATE_DATE) 
											|| fieldName.equals(CodeComment.NAME_LAST_ACTIVITY_DATE)) {
										List<InputSuggestion> suggestions = SuggestionUtils.suggest(getDateExamples(), matchWith);
										return !suggestions.isEmpty()? suggestions: null;
									} else if (fieldName.equals(CodeComment.NAME_PATH)) {
										return SuggestionUtils.suggestBlobs(projectModel.getObject(), matchWith);
									} else {
										return null;
									}
								} catch (ExplicitException ignored) {
								}
							}
						}
						return new ArrayList<>();
					}
					
					@Override
					protected String getFencingDescription() {
						return getI18nManager().getOrDefault("ValueShouldBeQuoted", "value should be quoted");
					}
					
				}.suggest(terminalExpect);
			} else if (spec.getRuleName().equals("Fuzzy")) {
				List<InputSuggestion> suggestions = new FenceAware(codeAssist.getGrammar(), '~', '~') {

					@Override
					protected List<InputSuggestion> match(String matchWith) {
						return null;
					}

					@Override
					protected String getFencingDescription() {
						return getI18nManager().getOrDefault("CodeCommentQueryDescriber.fuzzySugg", FUZZY_SUGGESTION_DESCRIPTION_PREFIX + " to query path/content/reply");
					}

				}.suggest(terminalExpect);
				return translateFuzzyOperatorDescription(suggestions);
			}
		} 
		return null;
	}
	
	@Override
	protected Optional<String> describe(ParseExpect parseExpect, String suggestedLiteral) {
		if (!withOrder && suggestedLiteral.equals(getRuleName(OrderBy))
				|| !withCurrentUserCriteria && (suggestedLiteral.equals(getRuleName(CreatedByMe)) || suggestedLiteral.equals(getRuleName(RepliedByMe)) || suggestedLiteral.equals(getRuleName(MentionedMe)))) {
			return null;
		} else if (suggestedLiteral.equals(",")) {
			if (parseExpect.findExpectByLabel("orderOperator") != null)
				return Optional.of(getI18nManager().getOrDefault(OperatorDescriber.O_COMMA_SORTING.getDescriptionI18nKey(), OperatorDescriber.O_COMMA_SORTING.getDefaultDesc()));
			else
                return Optional.of(getI18nManager().getOrDefault(OperatorDescriber.O_COMMA.getDescriptionI18nKey(), OperatorDescriber.O_COMMA.getDefaultDesc()));
		}
		
		parseExpect = parseExpect.findExpectByLabel("operator");
		if (parseExpect != null) {
			List<Element> fieldElements = parseExpect.getState().findMatchedElementsByLabel("criteriaField", false);
			if (!fieldElements.isEmpty()) {
				String fieldName = CodeCommentQuery.getValue(fieldElements.iterator().next().getMatchedText());
				try {
					CodeCommentQuery.checkField(getProject(), fieldName, CodeCommentQuery.getOperator(suggestedLiteral));
				} catch (ExplicitException e) {
					return null;
				}
			}
		}
		return super.describe(parseExpect, suggestedLiteral);
	}

	@Override
	protected List<String> getHints(TerminalExpect terminalExpect) {
		List<String> hints = new ArrayList<>();
		if (terminalExpect.getElementSpec() instanceof LexerRuleRefElementSpec) {
			LexerRuleRefElementSpec spec = (LexerRuleRefElementSpec) terminalExpect.getElementSpec();
			if ("criteriaValue".equals(spec.getLabel()) && ProjectQuery.isInsideQuote(terminalExpect.getUnmatchedText())) {
				List<Element> fieldElements = terminalExpect.getState().findMatchedElementsByLabel("criteriaField", true);
				if (!fieldElements.isEmpty()) {
					String fieldName = ProjectQuery.getValue(fieldElements.get(0).getMatchedText());
					if (fieldName.equals(CodeComment.NAME_CONTENT)) {
						hints.add(HINT_Name_Wildcard);
						hints.add(HINT_Escape_Quotes);
					}
				}
			}
		} 
		return hints;
	}

	@Override
	protected boolean isFuzzySuggestion(InputCompletion suggestion) {
		return suggestion.getDescription() != null 
				&& suggestion.getDescription().startsWith(FUZZY_SUGGESTION_DESCRIPTION_PREFIX);
	}

	@SuppressWarnings("unchecked")
	@Nullable
	@Override
	protected <T extends Enum<T> & Describer<T>> T getDescriber(String value) {
		return (T)Describer.of(CodeCommentQueryDescriber.class, value);
	}
}
