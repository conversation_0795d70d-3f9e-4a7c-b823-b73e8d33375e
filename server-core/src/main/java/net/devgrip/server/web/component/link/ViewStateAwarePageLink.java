package net.devgrip.server.web.component.link;

import org.apache.wicket.Page;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 * This class stores view state (scroll position, cursor etc) before leaving current page, so that 
 * the view state can be restored when we go back to current page  
 * 
 * <AUTHOR>
 * <AUTHOR>
 *
 * @param <T>
 */
public class ViewStateAwarePageLink<T> extends BookmarkablePageLink<T> {

	private final boolean needProgressbar;
	private final String scrollTopKey;

	public <C extends Page> ViewStateAwarePageLink(String id, final Class<C> pageClass) {
		this(id, pageClass, new PageParameters(), true, null);
	}
	public <C extends Page> ViewStateAwarePageLink(String id, final Class<C> pageClass, boolean needProgressbar) {
		this(id, pageClass, new PageParameters(), needProgressbar, null);
	}

	public <C extends Page> ViewStateAwarePageLink(String id, final Class<C> pageClass, PageParameters parameters) {
		this(id, pageClass, parameters, true, null);
	}
	public <C extends Page> ViewStateAwarePageLink(String id, final Class<C> pageClass, PageParameters parameters, String scrollTopKey) {
		this(id, pageClass, parameters, true, scrollTopKey);
	}
	public <C extends Page> ViewStateAwarePageLink(String id, final Class<C> pageClass, PageParameters parameters, boolean needProgressbar, String scrollTopKey) {
		super(id, pageClass, parameters);
		this.needProgressbar = needProgressbar;
		this.scrollTopKey = scrollTopKey;
	}

	@Override
	protected CharSequence getOnClickScript(CharSequence url) {

		var script = "onedev.server.viewState.getFromViewAndSetToHistory();";
		if (scrollTopKey != null) {
			script += "localStorage.setItem('" + scrollTopKey + "', Math.floor($(this).closest('.autofit').scrollTop()).toString());";
		}
		return script;
	}

	@Override
	protected void onComponentTag(ComponentTag tag) {
		super.onComponentTag(tag);
		if (needProgressbar) {
			tag.append("class", "need-progressbar", " ");
		}
	}
}
