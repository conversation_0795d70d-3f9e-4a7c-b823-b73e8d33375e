package net.devgrip.server.web.component.codecomment;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Multiline;
import net.devgrip.server.annotation.OmitName;

import java.io.Serializable;

@Editable(name="StatusChangeOptionBean.name")
public class StatusChangeOptionBean implements Serializable {

	private static final long serialVersionUID = 1L;

	private String note;

	@Editable(placeholder="CodeCommentPanel.leaveANote.placeholder")
	@Multiline
	@OmitName
	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}
	
}
