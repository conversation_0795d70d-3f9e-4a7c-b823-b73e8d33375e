package net.devgrip.server.plugin.mailservice.office365;

import com.google.common.collect.Lists;
import net.devgrip.commons.utils.ExplicitException;
import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Password;
import net.devgrip.server.annotation.RefreshToken;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.model.support.administration.mailservice.ImapImplicitSsl;
import net.devgrip.server.model.support.administration.mailservice.MailService;
import net.devgrip.server.model.support.administration.mailservice.SmtpExplicitSsl;
import net.devgrip.server.util.EditContext;
import net.devgrip.server.util.oauth.RefreshTokenAccessor;
import net.devgrip.server.mail.*;
import org.jetbrains.annotations.Nullable;

import javax.mail.Message;
import javax.validation.constraints.Email;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.function.Consumer;

@Editable(name="Microsoft 365", order=200)
public class Office365MailService implements MailService {

	private static final long serialVersionUID = 1L;
	
	private String clientId;

	private String tenantId;

	private String clientSecret;

	private String userPrincipalName;

	private String refreshToken;
	
	private String systemAddress;
	
	private InboxPollSetting inboxPollSetting;
	
	private int timeout = 60;
	
	private transient MailPosition mailPosition;
	
	@Editable(order=100, name="MicrosoftEntraId.clientId.name", description="MicrosoftEntraId.clientId.desc")
	@NotEmpty
	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	@Editable(order=150, name="MicrosoftEntraId.tenantId.name", description="MicrosoftEntraId.tenantId.desc")
	@NotEmpty
	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	@Editable(order=200, name="MicrosoftEntraId.clientSecret.name",description="MicrosoftEntraId.clientSecret.desc")
	@Password
	@NotEmpty
	public String getClientSecret() {
		return clientSecret;
	}

	public void setClientSecret(String clientSecret) {
		this.clientSecret = clientSecret;
	}

	@Editable(order=300, name = "Office365MailService.upn.name", description= "Office365MailService.upn.desc")
	@NotEmpty
	public String getUserPrincipalName() {
		return userPrincipalName;
	}

	public void setUserPrincipalName(String userPrincipalName) {
		this.userPrincipalName = userPrincipalName;
	}

	@Editable(order=400, name= "MailService.rt.name",description= "Office365MailService.rt.desc")
	@RefreshToken("getRefreshTokenCallback")
	@NotEmpty
	public String getRefreshToken() {
		return refreshToken;
	}

	public void setRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken;
	}

	@Editable(order=410, name="MailService.systemUserAddress.name", description="MailService.systemUserAddress.desc")
	@Email
	@NotEmpty
	public String getSystemAddress() {
		return systemAddress;
	}

	public void setSystemAddress(String systemAddress) {
		this.systemAddress = systemAddress;
	}
	
	@Editable(order=450, name="MailService.checkIncoming.name", description="Office365MailService.checkIncoming.desc")
	public InboxPollSetting getInboxPollSetting() {
		return inboxPollSetting;
	}

	public void setInboxPollSetting(InboxPollSetting inboxPollSetting) {
		this.inboxPollSetting = inboxPollSetting;
	}
	
	@Editable(order=10000, name="MailService.timeout.name",description="MailService.timeout.desc")
	@Min(value=5, message="{MailService.timeout.message}")
	public int getTimeout() {
		return timeout;
	}

	public void setTimeout(int timeout) {
		this.timeout = timeout;
	}

	private static String getTokenEndpoint(String tenantId) {
		return String.format("https://login.microsoftonline.com/%s/oauth2/v2.0/token", tenantId);
	}

	@SuppressWarnings("unused")
	private static RefreshToken.Callback getRefreshTokenCallback() {
		String tenantId = (String) EditContext.get().getInputValue("tenantId");
		if (tenantId == null)
			throw new ExplicitException("Directory (tenant) ID needs to be specified to generate refresh token");
		String clientId = (String) EditContext.get().getInputValue("clientId");
		if (clientId == null)
			throw new ExplicitException("Application (client) ID needs to be specified to generate refresh token");
		String clientSecret = (String) EditContext.get().getInputValue("clientSecret");
		if (clientSecret == null)
			throw new ExplicitException("Client secret needs to be specified to generate refresh token");

		String userPrincipalName = (String) EditContext.get().getInputValue("userPrincipalName");
		if (userPrincipalName == null)
			throw new ExplicitException("User principal name needs to be specified to generate refresh token");

		Collection<String> scopes = Lists.newArrayList(
				"https://outlook.office.com/SMTP.Send",
				"https://outlook.office.com/IMAP.AccessAsUser.All",
				"offline_access");

		String authorizeEndpoint = String.format(
				"https://login.microsoftonline.com/%s/oauth2/v2.0/authorize", tenantId);
		String tokenEndpoint = getTokenEndpoint(tenantId);

		return new RefreshToken.Callback() {

			@Override
			public String getAuthorizeEndpoint() {
				return authorizeEndpoint;
			}

			@Override
			public Map<String, String> getAuthorizeParams() {
				Map<String, String> params = new HashMap<>();
				params.put("login_hint", userPrincipalName);
				params.put("prompt", "consent");
				return params;
			}

			@Override
			public String getClientId() {
				return clientId;
			}

			@Override
			public String getClientSecret() {
				return clientSecret;
			}

			@Override
			public String getTokenEndpoint() {
				return tokenEndpoint;
			}

			@Override
			public Collection<String> getScopes() {
				return scopes;
			}

		};
	}

	private void updateRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken;
		AppServer.getInstance(SettingManager.class).saveMailService(this);
	}

	private SmtpSetting getSmtpSetting(boolean testMode) {
		MailCredential smtpCredential = new OAuthAccessToken(getTokenEndpoint(tenantId), clientId, clientSecret, new RefreshTokenAccessor() {
			@Override
			public String getRefreshToken() {
				return refreshToken;
			}

			@Override
			public void setRefreshToken(String refreshToken) {
				if (!testMode)
					updateRefreshToken(refreshToken);
			}
		});
		return new SmtpSetting("smtp.office365.com", new SmtpExplicitSsl(), userPrincipalName,
				smtpCredential, getTimeout());
	}	
	
	@Override
	public void sendMail(Collection<String> toList, Collection<String> ccList, Collection<String> bccList, 
						 String subject, String htmlBody, String textBody, @Nullable String replyAddress, 
						 @Nullable String senderName, @Nullable String references, boolean testMode) {
		getMailManager().sendMail(getSmtpSetting(testMode), toList, ccList, bccList, subject, htmlBody, textBody,
				replyAddress, senderName, getSystemAddress(), references);
	}

	@Override
	public InboxMonitor getInboxMonitor(boolean testMode) {
		var imapUser = getUserPrincipalName();
		var imapCredential = new OAuthAccessToken(getTokenEndpoint(tenantId), clientId, clientSecret, new RefreshTokenAccessor() {
			@Override
			public String getRefreshToken() {
				return refreshToken;
			}

			@Override
			public void setRefreshToken(String refreshToken) {
				if (!testMode)
					updateRefreshToken(refreshToken);
			}

		});
		if (inboxPollSetting != null) {
			var imapSetting = new ImapSetting("outlook.office365.com",
					new ImapImplicitSsl(), imapUser, imapCredential,
					inboxPollSetting.getPollInterval(), getTimeout());
			return new InboxMonitor() {
				@Override
				public Future<?> monitor(Consumer<Message> messageConsumer, boolean testMode) {
					if (mailPosition == null)
						mailPosition = new MailPosition();
					return getMailManager().monitorInbox(imapSetting, getSystemAddress(),
							messageConsumer, mailPosition, testMode);
				}
			};
		} else {
			return null;
		}
	}

	private MailManager getMailManager() {
		return AppServer.getInstance(MailManager.class);
	}
	
}
