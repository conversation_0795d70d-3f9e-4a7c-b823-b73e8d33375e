package net.devgrip.server.plugin.report.gtest;

import java.util.Collection;

import com.google.common.collect.Sets;

import net.devgrip.commons.loader.AbstractPluginModule;
import net.devgrip.commons.loader.ImplementationProvider;
import net.devgrip.server.buildspec.step.PublishReportStep;

/**
 * NOTE: Do not forget to rename moduleClass property defined in the pom if you've renamed this class.
 *
 */
public class GTestModule extends AbstractPluginModule {

	@Override
	protected void configure() {
		super.configure();
		
		contribute(ImplementationProvider.class, new ImplementationProvider() {

			@Override
			public Class<?> getAbstractClass() {
				return PublishReportStep.class;
			}
			
			@Override
			public Collection<Class<?>> getImplementations() {
				return Sets.newHashSet(PublishGTestReportStep.class);
			}
			
		});
	}

}
