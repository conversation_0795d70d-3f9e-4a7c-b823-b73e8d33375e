package net.devgrip.server.web.page.project.pullrequests.detail.activities.activity;

import java.util.Date;

import org.apache.wicket.Component;

import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.PullRequestUpdateManager;
import net.devgrip.server.model.PullRequestUpdate;
import net.devgrip.server.web.page.project.pullrequests.detail.activities.PullRequestActivity;

public class PullRequestUpdateActivity implements PullRequestActivity {

	private final Long updateId;
	
	public PullRequestUpdateActivity(PullRequestUpdate update) {
		updateId = update.getId();
	}
	
	@Override
	public Component render(String componentId) {
		return new PullRequestUpdatePanel(componentId);
	}

	public PullRequestUpdate getUpdate() {
		return AppServer.getInstance(PullRequestUpdateManager.class).load(updateId);
	}

	@Override
	public Date getDate() {
		return getUpdate().getDate();
	}

}
