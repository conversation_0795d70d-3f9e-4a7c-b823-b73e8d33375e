package net.devgrip.server.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ChoiceProvider {

	String value();

	String displayNames() default "";

	String descriptions() default "";
	
	boolean tagsMode() default false;
	
}
