<wicket:extend>
	<div class="card subscription-management">
		<div class="card-body">
			<div wicket:id="content"></div>
		</div>
	</div>
	<wicket:fragment wicket:id="noSubscriptionFrag">
		<div class="alert alert-notice alert-light h6 mb-4">
			<wicket:message key="SubscriptionManagementPage.desc"></wicket:message>
		</div>
		<div wicket:id="feedback"></div>
		<div class="d-flex align-items-center flex-wrap row-gap-2">
			<a href="https://devgrip.net/pricing" class="btn btn-sm btn-outline-primary flex-shrink-0 mr-3"><wicket:message key="SubscriptionManagementPage.order"></wicket:message></a>
			<a wicket:id="installSubscriptionKey" class="btn btn-sm btn-outline-primary flex-shrink-0 mr-3"><wicket:message key="SubscriptionManagementPage.installKey"></wicket:message></a>
			<span wicket:id="requestTrialSubscription"></span>
		</div>
	</wicket:fragment>
	<wicket:fragment wicket:id="trialSubscriptionFrag">
		<div wicket:id="detail" class="alert alert-notice font-weight-bold mb-4">
			<h6 wicket:id="title"></h6>
			<ul class="mb-0">
				<li class="mb-2">
					<wicket:message key="SubscriptionManagementPage.licensedTo"></wicket:message><span wicket:id="licensee"></span>
				</li>
				<wicket:enclosure child="licenseGroup">
					<li class="mb-2">
						<wicket:message key="SubscriptionManagementPage.licensedGroup"></wicket:message><span wicket:id="licenseGroup"></span> <span wicket:id="licenseGroupNotFound" class="ml-1 badge badge-sm badge-danger"><wicket:message key="SubscriptionManagementPage.notFound"></wicket:message></span>
					</li>
				</wicket:enclosure>
				<wicket:enclosure child="expirationDate">
					<li>
						<wicket:message key="SubscriptionManagementPage.expiration"></wicket:message><span wicket:id="expirationDate"></span>
					</li>
				</wicket:enclosure>
			</ul>
			<div wicket:id="alert" class="font-weight-bolder mt-3"></div>
		</div>
		<div class="d-flex align-items-center flex-wrap row-gap-2">
			<a href="https://devgrip.net/pricing" class="btn btn-sm btn-outline-primary flex-shrink-0 mr-3"><wicket:message key="SubscriptionManagementPage.order"></wicket:message></a>
			<a wicket:id="installSubscriptionKey" class="btn btn-sm btn-outline-primary flex-shrink-0 mr-3"><wicket:message key="SubscriptionManagementPage.installKey"></wicket:message></a>
			<a href="mailto:<EMAIL>" class="btn btn-sm btn-outline-primary flex-shrink-0"><wicket:message key="SubscriptionManagementPage.extend"></wicket:message></a>
		</div>
	</wicket:fragment>
	<wicket:fragment wicket:id="subscriptionFrag">
		<div wicket:id="detail" class="alert alert-notice font-weight-bold mb-4">
			<h6 wicket:id="title"></h6>
			<ul class="mb-0">
				<li class="mb-2">
					<wicket:message key="SubscriptionManagementPage.licensedTo"></wicket:message><span wicket:id="licensee"></span>
					<a href="mailto:<EMAIL>" class="alert-link ml-2"><wicket:message key="SubscriptionManagementPage.requestChange"></wicket:message></a>
				</li>
				<wicket:enclosure child="licenseGroup">
					<li class="mb-2">
						<wicket:message key="SubscriptionManagementPage.licensedGroup"></wicket:message><span wicket:id="licenseGroup"></span> <span wicket:id="licenseGroupNotFound" class="ml-1 badge badge-sm badge-danger"><wicket:message key="SubscriptionManagementPage.notFound"></wicket:message></span>
						<div class="text-warning"><wicket:message key="SubscriptionManagementPage.licensedGroup.desc"></wicket:message></div>
					</li>
				</wicket:enclosure>
				<li class="mb-2">
					<wicket:message key="SubscriptionManagementPage.remain"></wicket:message><span wicket:id="userMonths"></span>
					<a href="https://devgrip.net/pricing" class="alert-link ml-2"><wicket:message key="SubscriptionManagementPage.orderMore"></wicket:message></a>
				</li>
			</ul>
			<div wicket:id="expirationInfo" class="font-weight-bolder mt-3"></div>
			<div wicket:id="alert" class="font-weight-bolder mt-3"></div>
			<div wicket:id="disabledNotice" class="mt-4 font-size-sm"><wicket:svg href="info-circle" class="icon mr-1"></wicket:svg> <wicket:message key="SubscriptionManagementPage.disabledUserAreExcludeUserMonthCheck"></wicket:message></div>
		</div>
		<div class="d-flex align-items-center flex-wrap row-gap-2">
			<a href="https://devgrip.net/pricing" class="btn btn-sm btn-outline-primary mr-3 flex-shrink-0"><wicket:message key="SubscriptionManagementPage.orderMoreUserMonths"></wicket:message></a>
			<a wicket:id="installSubscriptionKey" class="btn btn-sm btn-outline-primary mr-3 flex-shrink-0"><wicket:message key="SubscriptionManagementPage.installKey"></wicket:message></a>
		</div>
	</wicket:fragment>
	<wicket:fragment wicket:id="firstTrialSubscriptionRequestFrag">
		<form class="form-inline" action="https://devgrip.net/subscription-key" method="post">
			<input wicket:id="subscriptionKeyUUID" name="subscriptionKeyUUID" type="hidden">
			<button class="btn btn-sm btn-outline-primary flex-shrink-0" type="submit"><wicket:message key="SubscriptionManagementPage.requestTrialSubscription"></wicket:message></button>
		</form>
	</wicket:fragment>
	<wicket:fragment wicket:id="moreTrialSubscriptionRequestFrag">
		<a href="mailto:<EMAIL>" class="btn btn-sm btn-outline-primary flex-shrink-0"><wicket:message key="SubscriptionManagementPage.requestTrialSubscription"></wicket:message></a>
	</wicket:fragment>
</wicket:extend>
