package net.devgrip.server.model.support.pullrequest.changedata;

import net.devgrip.server.notification.ActivityDetail;

import java.util.HashMap;
import java.util.Map;

public class PullRequestTitleChangeData extends PullRequestChangeData {

	private static final long serialVersionUID = 1L;

	private final String oldTitle;
	
	private final String newTitle;
	
	public PullRequestTitleChangeData(String oldTitle, String newTitle) {
		this.oldTitle = oldTitle;
		this.newTitle = newTitle;
	}
	
	@Override
	public String getActivity() {
		return "changed title";
	}

	/**
	 * 获取动态的I18n的key，用于国际化显示
	 *
	 * @return
	 */
	@Override
	public String getActivityI18nKey() {
		return "PullRequestChangeData.activities.changedTitle";
	}

	@Override
	public ActivityDetail getActivityDetail() {
		Map<String, String> oldProperties = new HashMap<>();
		oldProperties.put("Title", oldTitle);
		Map<String, String> newProperties = new HashMap<>();
		newProperties.put("Title", newTitle);
		
		return ActivityDetail.compare(oldProperties, newProperties, true);
	}
	
}
