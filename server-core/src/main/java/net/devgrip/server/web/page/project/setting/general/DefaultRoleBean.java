package net.devgrip.server.web.page.project.setting.general;

import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.RoleChoice;
import net.devgrip.server.entitymanager.RoleManager;
import net.devgrip.server.model.Role;

import javax.annotation.Nullable;
import java.io.Serializable;

@Editable
public class DefaultRoleBean implements Serializable {

	private static final long serialVersionUID = 1L;

	private String roleName;

	@Editable(name="DefaultRoleBean.roleName.name", placeholder="InheritFromParent", rootPlaceholder ="DefaultRoleBean.roleName.rootPlaceholder", description="DefaultRoleBean.roleName.desc")
	@RoleChoice
	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	
	public void setRole(@Nullable Role role) {
		if (role != null)
			roleName = role.getName();
		else
			roleName = null;
	}
	
	@Nullable
	public Role getRole() {
		if (roleName != null)
			return AppServer.getInstance(RoleManager.class).find(roleName);
		else
			return null;
	}
	
}
